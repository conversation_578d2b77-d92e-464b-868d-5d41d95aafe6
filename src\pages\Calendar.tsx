import React, { useState, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { format, parseISO, differenceInMonths, addMonths, addDays, addWeeks, addYears, isAfter } from 'date-fns';
import { Modal } from '../components/Modal';
import { formatCurrency } from '../utils/currency';
import { localDataStore } from '../lib/local-data-store';

// Add CSS for toast animations
const toastAnimationStyles = `
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
  
  .animate-fade-in-up {
    animation: fadeInUp 0.3s ease-out;
  }
  
  .animate-fade-out {
    animation: fadeOut 0.3s ease-in;
  }
`;

// Add the styles to the document head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = toastAnimationStyles;
  document.head.appendChild(styleElement);
}
import {
  Calendar as CalendarIcon,
  Plus,
  AlertCircle,
  CreditCard,
  Banknote,
  Target,
  DollarSign,
  PiggyBank,
  ChevronLeft,
  ChevronRight,
  Settings
} from 'lucide-react';
import { ColorPicker } from '../components/ColorPicker';
import { useAuthStore } from '../store/authStore';
import { Link } from 'react-router-dom';

interface Loan {
  id: string;
  user_id: string;
  name: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  remaining_balance: number;
  monthly_payment: number;
  next_payment_date: string;
  end_date?: string;
  created_at?: string;
}

interface CD {
  id: string;
  user_id: string;
  institution: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  maturity_date: string;
}

interface FinancialGoal {
  id: string;
  user_id: string;
  name: string;
  target_amount: number;
  target_date: string;
  notes: string;
}

interface Expense {
  id: string;
  user_id: string;
  amount: number;
  category: string;
  description: string;
  date: string;
}

interface CalendarEvent {
  id: string;
  title: string;
  start: string;
  end?: string;
  backgroundColor: string;
  borderColor: string;
  textColor: string;
  allDay?: boolean;
  className?: string;
  extendedProps: {
    type: 'loan' | 'goal' | 'expense' | 'custom' | 'cd-maturity';
    amount?: number;
    description?: string;
    icon?: string;
    recurrence?: boolean;
    // Additional properties for loan payments
    principalPayment?: number;
    interestPayment?: number;
    remainingBalance?: number;
    percentPaid?: number;
  };
}

// We'll define the EventTemplate interface in the CalendarTools page

interface CustomEvent {
  id: string;
  user_id: string;
  title: string;
  start_date: string;
  end_date?: string;
  type: string;
  amount?: number;
  description?: string;
  created_at: string;
  backgroundColor?: string; // Custom background color
  borderColor?: string; // Custom border color
  textColor?: string; // Custom text color
  isRecurring?: boolean; // Whether the event repeats
  recurrencePattern?: 'daily' | 'weekly' | 'monthly' | 'yearly'; // How often it repeats
  recurrenceEndDate?: string; // When the recurrence ends (optional)
  recurrenceCount?: number; // How many times it repeats (optional alternative to end date)
  reminder?: boolean; // Whether to send a reminder for this event
  reminderTime?: number; // How many minutes/hours/days before the event to send the reminder
  reminderUnit?: 'minutes' | 'hours' | 'days'; // Unit for reminderTime
  reminderSent?: boolean; // Whether the reminder has been sent
  category?: string; // Category for the event (e.g., 'bills', 'investments', 'taxes', etc.)
  templateId?: string; // Reference to the template this event was created from
}

export function Calendar() {
  const { user } = useAuthStore();
  const [isModalOpen, setIsModalOpen] = useState(false);
  // Modal state for event creation/editing
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [loans, setLoans] = useState<Loan[]>([]);
  const [cds, setCds] = useState<CD[]>([]);
  const [goals, setGoals] = useState<FinancialGoal[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [customEvents, setCustomEvents] = useState<CustomEvent[]>([]);
  // Event form state
  const [eventTitle, setEventTitle] = useState('');
  const [eventDescription, setEventDescription] = useState('');
  const [eventAmount, setEventAmount] = useState('');
  const [eventColor, setEventColor] = useState('');
  const [isRecurring, setIsRecurring] = useState(false);
  const [recurrencePattern, setRecurrencePattern] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('monthly');
  const [recurrenceEndDate, setRecurrenceEndDate] = useState('');
  const [recurrenceCount, setRecurrenceCount] = useState('');
  const [reminder, setReminder] = useState(false);
  const [reminderTime, setReminderTime] = useState(30);
  const [reminderUnit, setReminderUnit] = useState<'minutes' | 'hours' | 'days'>('minutes');
  const [eventCategory, setEventCategory] = useState('');
  
  // Filtering and pagination state
  const [eventTimeHorizon, setEventTimeHorizon] = useState<'week' | 'month' | 'quarter' | 'year' | 'all'>('month');
  const [eventTypeFilter, setEventTypeFilter] = useState<string | 'all'>('all');
  const [eventCategoryFilter, setEventCategoryFilter] = useState<string | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const eventsPerPage = 5;
  
  // We'll implement event templates in the CalendarTools page instead

  // Predefined categories for financial events
  const eventCategories = [
    { id: 'bills', label: 'Bills & Utilities', icon: 'credit-card' },
    { id: 'income', label: 'Income & Salary', icon: 'dollar-sign' },
    { id: 'investments', label: 'Investments', icon: 'trending-up' },
    { id: 'savings', label: 'Savings', icon: 'piggy-bank' },
    { id: 'taxes', label: 'Taxes', icon: 'file-text' },
    { id: 'insurance', label: 'Insurance', icon: 'shield' },
    { id: 'debt', label: 'Debt Payments', icon: 'credit-card' },
    { id: 'education', label: 'Education', icon: 'book' },
    { id: 'housing', label: 'Housing', icon: 'home' },
    { id: 'transportation', label: 'Transportation', icon: 'car' },
    { id: 'healthcare', label: 'Healthcare', icon: 'activity' },
    { id: 'personal', label: 'Personal', icon: 'user' },
    { id: 'other', label: 'Other', icon: 'more-horizontal' }
  ];

  // State variables for calendar view
  const [calendarView, setCalendarView] = useState<'dayGridMonth' | 'dayGridWeek' | 'dayGridDay' | 'listWeek'>('dayGridMonth');
  const [forecastMonths, setForecastMonths] = useState(6);
  const [eventType, setEventType] = useState('custom');


  // Load data from local storage on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load loans
        const loansData = await localDataStore.data.loadCollection<Loan>('loans');
        setLoans(loansData);

        // Load CDs
        const cdsData = await localDataStore.data.loadCollection<CD>('cds');
        setCds(cdsData);

        // Load goals
        const goalsData = await localDataStore.data.loadCollection<FinancialGoal>('financial_goals');
        setGoals(goalsData);

        // Load expenses
        const expensesData = await localDataStore.data.loadCollection<Expense>('expenses');
        setExpenses(expensesData);

        // Load custom events
        const customEventsData = await localDataStore.data.loadCollection<CustomEvent>('custom_events') || [];
        setCustomEvents(customEventsData);

        // We'll load templates in the CalendarTools page instead
      } catch (error) {
        console.error('Error loading data:', error);
      }
    };

    loadData();
  }, []); // Empty dependency array means this effect runs once on mount
  
  // We'll implement templates in the CalendarTools page instead

  // Set up periodic reminder checks
  useEffect(() => {
    // Check for reminders every minute
    const reminderInterval = setInterval(() => {
      checkReminders();
    }, 60000);
    
    // Clean up interval on component unmount
    return () => clearInterval(reminderInterval);
  }, [customEvents]);

  const getEventColor = (type: string) => {
    switch (type) {
      case 'loan':
        return { bg: '#ef4444', border: '#dc2626', text: '#ffffff', icon: 'credit-card' };
      case 'cd-maturity':
        return { bg: '#0ea5e9', border: '#0284c7', text: '#ffffff', icon: 'banknote' };
      case 'goal':
        return { bg: '#3b82f6', border: '#2563eb', text: '#ffffff', icon: 'target' };
      case 'expense':
        return { bg: '#f97316', border: '#ea580c', text: '#ffffff', icon: 'dollar-sign' };
      case 'income':
        return { bg: '#22c55e', border: '#16a34a', text: '#ffffff', icon: 'dollar-sign' };
      case 'savings':
        return { bg: '#6366f1', border: '#4f46e5', text: '#ffffff', icon: 'piggy-bank' };
      case 'reminder':
        return { bg: '#a855f7', border: '#9333ea', text: '#ffffff', icon: 'alert-circle' };
      default:
        return { bg: '#6b7280', border: '#4b5563', text: '#ffffff', icon: 'calendar' };
    }
  };

  const generateRecurringLoanPayments = (): CalendarEvent[] => {
    console.log("generateRecurringLoanPayments function called");
    console.log("Loans data:", loans);
    if (!loans || loans.length === 0) {
      console.log("No loans found, returning empty array.");
      return [];
    }

    const recurringEvents: CalendarEvent[] = [];
    const today = new Date();
    console.log("Today's date:", today);

    // Limit forecast months to prevent performance issues
    const safeMonths = Math.min(forecastMonths, 24);
    console.log("Safe months:", safeMonths);

    // Limit to top 10 loans if there are too many
    const limitedLoans = loans.length > 10 ?
      [...loans].sort((a, b) => b.monthly_payment - a.monthly_payment).slice(0, 10) :
      loans;

    console.log("Processing loans:", limitedLoans);

    limitedLoans.forEach(loan => {
      console.log("Processing loan:", loan.name, loan);
      if (loan.remaining_balance <= 0) {
        console.log("Skipping loan with zero balance:", loan.name);
        return;
      }

      try {
        // Make sure next_payment_date is valid
        if (!loan.next_payment_date) {
          console.error("Loan missing next_payment_date:", loan);
          // Create a fallback payment date for testing purposes
          loan.next_payment_date = new Date().toISOString();
          console.log("Created fallback payment date:", loan.next_payment_date);
        }

        let paymentDate;
        try {
          paymentDate = parseISO(loan.next_payment_date);
          // Check if date is valid
          if (isNaN(paymentDate.getTime())) {
            console.error("Invalid payment date:", loan.next_payment_date);
            paymentDate = new Date(); // Use today as fallback
          }
        } catch (dateError) {
          console.error("Error parsing payment date:", loan.next_payment_date, dateError);
          paymentDate = new Date(); // Use today as fallback
        }
        
        // Set time to midnight for consistent date comparison
        paymentDate.setHours(0, 0, 0, 0);
        console.log("Payment date (midnight):", paymentDate);

        // Calculate the end date based on term months if not provided
        const startDate = paymentDate; // Use the payment date we've already parsed
        let endDate;
        
        try {
          if (loan.end_date) {
            endDate = parseISO(loan.end_date);
            // Check if date is valid
            if (isNaN(endDate.getTime())) {
              console.error("Invalid end date:", loan.end_date);
              endDate = addMonths(startDate, loan.term_months || 360); // Default to term or 30 years
            }
          } else {
            endDate = loan.term_months ? addMonths(startDate, loan.term_months) : addMonths(startDate, 360); // Default to 30 years if no term
          }
        } catch (dateError) {
          console.error("Error parsing end date:", loan.end_date, dateError);
          endDate = addMonths(startDate, loan.term_months || 360); // Default to term or 30 years
        }

        console.log("End date:", endDate);

        // Calculate remaining payments - with error handling
        let remainingMonths;
        try {
          remainingMonths = Math.max(0, differenceInMonths(endDate, today));
        } catch (error) {
          console.error("Error calculating remaining months:", error);
          remainingMonths = 24; // Default to 24 months if calculation fails
        }
        
        const actualMonthsToShow = Math.min(safeMonths, remainingMonths || 24); // Default to 24 months if calculation fails

        console.log("Remaining months:", remainingMonths, "Actual months to show:", actualMonthsToShow);

        // Generate payment events
        for (let i = 0; i < actualMonthsToShow; i++) {
          // Show all payments regardless of date for testing
          // Show all payments for the next 3 months, then monthly for the next 9 months,
          // then quarterly after that for better visibility of near-term payments
          if (i <= 3 || (i <= 12 && i % 1 === 0) || i % 3 === 0) {
            try {
              // Calculate remaining principal for this payment (approximate)
              const monthlyRate = (loan.interest_rate / 100) / 12;
              const remainingPrincipal = i === 0 ?
                loan.remaining_balance :
                calculateRemainingBalance(loan.remaining_balance, loan.monthly_payment, monthlyRate, i);

              // Calculate how much of the payment is principal vs interest
              const interestPayment = remainingPrincipal * monthlyRate;
              const principalPayment = Math.min(loan.monthly_payment - interestPayment, remainingPrincipal);

              // Calculate percentage of loan paid off
              const percentPaid = ((loan.principal - remainingPrincipal) / loan.principal) * 100;
              const percentPaidFormatted = percentPaid.toFixed(1);

              // Create a more detailed description
              const description = `
                Monthly payment for ${loan.name}
                Principal: ${formatCurrency(principalPayment)}
                Interest: ${formatCurrency(interestPayment)}
                Remaining balance after payment: ${formatCurrency(remainingPrincipal - principalPayment)}
                Loan ${percentPaidFormatted}% paid off
              `;

              let eventDate;
              try {
                eventDate = format(paymentDate, 'yyyy-MM-dd');
              } catch (formatError) {
                console.error("Error formatting payment date:", paymentDate, formatError);
                eventDate = format(new Date(), 'yyyy-MM-dd'); // Use today as fallback
              }
              
              console.log(`Processing payment for ${loan.name}. Payment date: ${paymentDate}, Formatted event date: ${eventDate}`);

              const newEvent: CalendarEvent = {
                id: `loan-${loan.id}-payment-${i}`,
                title: `${loan.name} Payment: ${formatCurrency(loan.monthly_payment)}`,
                start: eventDate,
                backgroundColor: getEventColor('loan').bg,
                borderColor: getEventColor('loan').border,
                textColor: getEventColor('loan').text,
                className: 'loan-payment-event',
                allDay: true,
                extendedProps: {
                  type: 'loan',
                  amount: loan.monthly_payment,
                  description: description.trim(),
                  icon: getEventColor('loan').icon,
                  recurrence: i > 0,
                  principalPayment: principalPayment,
                  interestPayment: interestPayment,
                  remainingBalance: remainingPrincipal - principalPayment,
                  percentPaid: percentPaid
                }
              };
              console.log("Pushing event:", newEvent);
              recurringEvents.push(newEvent);
            } catch (eventError) {
              console.error("Error creating loan payment event:", eventError);
            }
          }

          // Move to next month's payment
          try {
            paymentDate = addMonths(paymentDate, 1);
          } catch (dateError) {
            console.error("Error adding month to payment date:", paymentDate, dateError);
            // Increment by 30 days as fallback
            paymentDate = new Date(paymentDate.getTime() + 30 * 24 * 60 * 60 * 1000);
          }
        }
      } catch (error) {
        console.error("Error processing loan:", loan.name, error);
      }
    });

    console.log("Generated events:", recurringEvents.length);
    console.log("Generated events array:", recurringEvents);
    return recurringEvents;
  };

  // Helper function to calculate remaining balance after a number of payments
  const calculateRemainingBalance = (
    currentBalance: number,
    monthlyPayment: number,
    monthlyRate: number,
    months: number
  ): number => {
    let balance = currentBalance;

    for (let i = 0; i < months; i++) {
      const interest = balance * monthlyRate;
      const principal = Math.min(monthlyPayment - interest, balance);
      balance -= principal;

      if (balance <= 0) return 0;
    }

    return balance;
  };

  const generateCDMaturityEvents = (): CalendarEvent[] => {
    console.log("Generating CD maturity events");
    if (!cds || cds.length === 0) {
      console.log("No CDs found, returning empty array");
      return [];
    }
    
    console.log("Processing CDs:", cds);
    
    const cdEvents: CalendarEvent[] = [];
    
    cds.forEach(cd => {
      try {
        // Validate maturity date
        if (!cd.maturity_date) {
          console.error("CD missing maturity date:", cd);
          return;
        }
        
        // Parse the date safely
        let maturityDate;
        try {
          maturityDate = new Date(cd.maturity_date);
          // Check if date is valid
          if (isNaN(maturityDate.getTime())) {
            console.error("Invalid maturity date:", cd.maturity_date);
            // Use a fallback date 6 months from now
            const today = new Date();
            maturityDate = new Date(today.getFullYear(), today.getMonth() + 6, today.getDate());
          }
        } catch (dateError) {
          console.error("Error parsing maturity date:", cd.maturity_date, dateError);
          // Use a fallback date 6 months from now
          const today = new Date();
          maturityDate = new Date(today.getFullYear(), today.getMonth() + 6, today.getDate());
        }
        
        // Format the date for the event
        let eventDate;
        try {
          eventDate = format(maturityDate, 'yyyy-MM-dd');
        } catch (formatError) {
          console.error("Error formatting maturity date:", maturityDate, formatError);
          eventDate = format(new Date(), 'yyyy-MM-dd'); // Use today as fallback
        }
        
        console.log(`Processing CD for ${cd.institution}. Maturity date: ${maturityDate}, Formatted event date: ${eventDate}`);
        
        const newEvent: CalendarEvent = {
          id: `cd-maturity-${cd.id}`,
          title: `${cd.institution} CD Matures: ${formatCurrency(cd.principal)}`,
          start: eventDate,
          backgroundColor: getEventColor('cd-maturity').bg,
          borderColor: getEventColor('cd-maturity').border,
          textColor: getEventColor('cd-maturity').text,
          className: 'cd-maturity-event',
          allDay: true,
          extendedProps: {
            type: 'cd-maturity',
            amount: cd.principal,
            description: `Certificate of Deposit at ${cd.institution} matures with principal of ${formatCurrency(cd.principal)} plus interest`,
            icon: getEventColor('cd-maturity').icon
          }
        };
        
        console.log("Pushing CD event:", newEvent);
        cdEvents.push(newEvent);
      } catch (error) {
        console.error("Error processing CD:", cd, error);
      }
    });
    
    console.log("Generated CD events:", cdEvents.length);
    return cdEvents;
  };

  // Create a simpler implementation for loan payments
  const simpleLoanEvents: CalendarEvent[] = [];

  if (loans && loans.length > 0) {
    console.log("Processing loans for calendar:", loans);

    // Get current date
    const today = new Date();
    const nextSixMonths = new Date(today);
    nextSixMonths.setMonth(today.getMonth() + 6);

    // Generate events for each loan
    loans.forEach(loan => {
      if (!loan.next_payment_date) {
        console.log("Loan missing next payment date:", loan);
        return;
      }

      let paymentDate = parseISO(loan.next_payment_date);

      // Generate 6 months of payments
      for (let i = 0; i < 6; i++) {
        if (paymentDate >= today) {
          const eventDate = format(paymentDate, 'yyyy-MM-dd');
          console.log(`Adding loan payment for ${loan.name} on ${eventDate}`);

          simpleLoanEvents.push({
            id: `loan-${loan.id}-payment-${i}`,
            title: `${loan.name} Payment: ${formatCurrency(loan.monthly_payment)}`,
            start: eventDate,
            backgroundColor: getEventColor('loan').bg,
            borderColor: getEventColor('loan').border,
            textColor: getEventColor('loan').text,
            className: 'loan-payment-event',
            allDay: true,
            extendedProps: {
              type: 'loan',
              amount: loan.monthly_payment,
              description: `Monthly payment for ${loan.name}`,
              icon: getEventColor('loan').icon,
              recurrence: i > 0
            }
          });
        }

        // Move to next month
        paymentDate = addMonths(paymentDate, 1);
      }
    });
  }

  // Generate other events
  const cdEvents = generateCDMaturityEvents();
  const goalEvents = (goals?.map(goal => ({
    id: `goal-${goal.id}`,
    title: `${goal.name}: ${formatCurrency(goal.target_amount)}`,
    start: goal.target_date,
    backgroundColor: getEventColor('goal').bg,
    borderColor: getEventColor('goal').border,
    textColor: getEventColor('goal').text,
    className: 'goal-event',
    allDay: true,
    extendedProps: {
      type: 'goal' as const,
      amount: goal.target_amount,
      description: goal.notes,
      icon: getEventColor('goal').icon
    }
  })) || []);

  const expenseEvents = (expenses?.map(expense => ({
    id: `expense-${expense.id}`,
    title: `${expense.category}: ${formatCurrency(expense.amount)}`,
    start: expense.date,
    backgroundColor: getEventColor('expense').bg,
    borderColor: getEventColor('expense').border,
    textColor: getEventColor('expense').text,
    className: 'expense-event',
    allDay: true,
    extendedProps: {
      type: 'expense' as const,
      amount: expense.amount,
      description: expense.description,
      icon: getEventColor('expense').icon
    }
  })) || []);

  const customCalendarEvents = (customEvents?.map(event => ({
    id: `custom-${event.id}`,
    title: event.title,
    start: event.start_date,
    end: event.end_date,
    // Use custom colors if available, otherwise use default colors based on type
    backgroundColor: event.backgroundColor || getEventColor(event.type).bg,
    borderColor: event.borderColor || getEventColor(event.type).border,
    textColor: event.textColor || getEventColor(event.type).text,
    className: `custom-${event.type}-event`,
    allDay: true,
    extendedProps: {
      type: 'custom' as const,
      amount: event.amount,
      description: event.description,
      icon: getEventColor(event.type).icon,
      customColor: event.backgroundColor ? true : false // Flag to indicate if using custom color
    }
  })) || []);

  // Combine all events
  const events: CalendarEvent[] = [
    ...generateRecurringLoanPayments(),
    ...cdEvents,
    ...goalEvents,
    ...expenseEvents,
    ...customCalendarEvents
  ];

  console.log("All events:", {
    loanEvents: simpleLoanEvents.length,
    cdEvents: cdEvents.length,
    goalEvents: goalEvents.length,
    expenseEvents: expenseEvents.length,
    customEvents: customCalendarEvents.length,
    totalEvents: events.length
  });

  const handleDateClick = (arg: { date: Date }) => {
    setSelectedDate(arg.date);
    setSelectedEvent(null);
    resetForm();
    setIsModalOpen(true);
  };

  const handleEventClick = (arg: { event: any }) => {
    console.log('Event clicked:', arg.event);
    const eventData = {
      id: arg.event.id,
      title: arg.event.title,
      start: arg.event.start,
      end: arg.event.end,
      backgroundColor: arg.event.backgroundColor,
      borderColor: arg.event.borderColor,
      textColor: arg.event.textColor,
      extendedProps: arg.event.extendedProps
    };
    setSelectedEvent(eventData as CalendarEvent);
    setIsModalOpen(true);
  };

  // Handle event drag-and-drop
  const handleEventDrop = async (info: any) => {
    const { event } = info;
    console.log('Event dropped:', event);
    
    // Only custom events can be dragged (not system-generated events like loan payments)
    if (event.extendedProps.type !== 'custom') {
      alert('Only custom events can be rescheduled by dragging.');
      info.revert(); // Revert the drag operation
      return;
    }
    
    try {
      // Extract the event ID (remove the 'custom-' prefix)
      const originalId = event.id.replace('custom-', '');
      
      // Find the original custom event
      const originalEvent = customEvents?.find(e => e.id === originalId);
      
      if (!originalEvent) {
        console.error('Original event not found:', originalId);
        info.revert();
        return;
      }
      
      // Update the event with the new date
      const updatedEvent: CustomEvent = {
        ...originalEvent,
        start_date: format(event.start, 'yyyy-MM-dd'),
        end_date: event.end ? format(event.end, 'yyyy-MM-dd') : undefined
      };
      
      // Save the updated event
      await localDataStore.data.updateItem('calendar_events', updatedEvent.id, updatedEvent);
      
      // Update local state
      setCustomEvents(prev => 
        prev.map(e => e.id === originalId ? updatedEvent : e)
      );
      
      // Show success message
      console.log('Event rescheduled successfully:', updatedEvent.title);
      
      // Show toast notification
      showToast(`Event "${updatedEvent.title}" rescheduled successfully`);
    
      // If it was a recurring event, ask if user wants to move all instances
      if (originalEvent.isRecurring) {
        const moveAll = window.confirm('Do you want to move all future occurrences of this recurring event?');
        if (moveAll) {
          // This would require additional logic to update all future instances
          // For now, just show a message that this feature is coming soon
          alert('Moving all recurring instances will be available in a future update.');
        }
      }
    } catch (error) {
      console.error('Error updating event:', error);
      info.revert(); // Revert the drag operation on error
      alert('Failed to reschedule event. Please try again.');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedDate || !user) return;

    try {
      // Get colors based on event type or custom selection
      let backgroundColor, borderColor, textColor;
      
      if (eventColor) {
        // Use custom color if selected
        backgroundColor = eventColor;
        borderColor = eventColor;
        textColor = '#ffffff'; // White text for contrast
      } else {
        // Otherwise use default colors based on type
        const colors = getEventColor(eventType);
        backgroundColor = colors.bg;
        borderColor = colors.border;
        textColor = colors.text;
      }
      
      // Handle recurrence information
      let recurrenceInfo = {};
      if (isRecurring) {
        recurrenceInfo = {
          isRecurring: true,
          recurrencePattern,
          // Only include end date or count if provided
          ...(recurrenceEndDate ? { recurrenceEndDate } : {}),
          ...(recurrenceCount ? { recurrenceCount: Number(recurrenceCount) } : {})
        };
      }
      
      // Handle reminder information
      let reminderInfo = {};
      if (reminder) {
        reminderInfo = {
          reminder: true,
          reminderTime,
          reminderUnit,
          reminderSent: false // Initialize as not sent
        };
      }
      
      // Handle category information
      let categoryInfo = {};
      if (eventCategory) {
        categoryInfo = {
          category: eventCategory
        };
      }
      
      // Use localDataStore to add the custom event
      const newCustomEvent: CustomEvent = {
        id: localDataStore.utils.generateId(), // Generate a new ID
        user_id: user.id,
        title: eventTitle,
        type: eventType,
        start_date: format(selectedDate, 'yyyy-MM-dd'),
        end_date: undefined, // Custom events don't have an end date in this form
        amount: eventAmount ? Number(eventAmount) : undefined,
        description: eventDescription,
        backgroundColor,
        borderColor,
        textColor,
        created_at: new Date().toISOString(),
        ...recurrenceInfo, // Add recurrence information if applicable
        ...reminderInfo, // Add reminder information if applicable
        ...categoryInfo // Add category information if applicable
      };
      
      // Save the original event
      await localDataStore.data.addItem('calendar_events', newCustomEvent);
      
      // If it's a recurring event, generate future occurrences
      if (isRecurring) {
        console.log("Creating recurring event instances for:", newCustomEvent.title);
        const recurringEvents = generateRecurringEventInstances(newCustomEvent);
        
        // Save all recurring instances
        for (const event of recurringEvents) {
          await localDataStore.data.addItem('calendar_events', event);
        }
        
        // Update the local state to reflect all new events
        setCustomEvents(prevEvents => [...prevEvents, newCustomEvent, ...recurringEvents]);
      } else {
        // Update the local state to reflect the new single event
        setCustomEvents(prevEvents => [...prevEvents, newCustomEvent]);
      }

      setIsModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error adding event:', error);
    }
  };

  const resetForm = () => {
    setEventTitle('');
    setEventType('expense');
    setEventAmount('');
    setEventDescription('');
    setEventColor(''); // Reset color selection
    setIsRecurring(false); // Reset recurrence options
    setRecurrencePattern('monthly');
    setRecurrenceEndDate('');
    setRecurrenceCount('');
    setReminder(false); // Reset reminder options
    setReminderTime(1);
    setReminderUnit('days');
    setEventCategory(''); // Reset category selection
  };

  // Generate recurring event instances based on the pattern
  const generateRecurringEventInstances = (baseEvent: CustomEvent): CustomEvent[] => {
    const recurringEvents: CustomEvent[] = [];
    
    if (!baseEvent.isRecurring || !baseEvent.recurrencePattern) {
      return recurringEvents;
    }
    
    // Parse the start date
    const startDate = parseISO(baseEvent.start_date);
    
    // Determine how many instances to generate
    let instanceCount = 10; // Default to 10 instances if no count or end date specified
    
    if (baseEvent.recurrenceCount) {
      instanceCount = baseEvent.recurrenceCount;
    } else if (baseEvent.recurrenceEndDate) {
      // Calculate instances up to end date
      const endDate = parseISO(baseEvent.recurrenceEndDate);
      
      // Calculate difference based on recurrence pattern
      switch (baseEvent.recurrencePattern) {
        case 'daily':
          instanceCount = Math.ceil(differenceInMonths(endDate, startDate) * 30);
          break;
        case 'weekly':
          instanceCount = Math.ceil(differenceInMonths(endDate, startDate) * 4);
          break;
        case 'monthly':
          instanceCount = differenceInMonths(endDate, startDate);
          break;
        case 'yearly':
          instanceCount = Math.ceil(differenceInMonths(endDate, startDate) / 12);
          break;
      }
    }
    
    // Limit to a reasonable number
    instanceCount = Math.min(instanceCount, 24);
    
    // Generate the instances
    for (let i = 1; i <= instanceCount; i++) {
      let nextDate;
      
      // Calculate the next date based on recurrence pattern
      switch (baseEvent.recurrencePattern) {
        case 'daily':
          nextDate = addDays(startDate, i);
          break;
        case 'weekly':
          nextDate = addWeeks(startDate, i);
          break;
        case 'monthly':
          nextDate = addMonths(startDate, i);
          break;
        case 'yearly':
          nextDate = addYears(startDate, i);
          break;
        default:
          nextDate = addMonths(startDate, i);
      }
      
      // Skip if beyond end date
      if (baseEvent.recurrenceEndDate && isAfter(nextDate, parseISO(baseEvent.recurrenceEndDate))) {
        continue;
      }
      
      // Create a new instance with the calculated date
      const newInstance: CustomEvent = {
        ...baseEvent,
        id: localDataStore.utils.generateId(), // Generate a new ID for each instance
        start_date: format(nextDate, 'yyyy-MM-dd'),
        // Add a reference to the original event
        description: `${baseEvent.description || ''} (Recurring ${baseEvent.recurrencePattern} event, instance ${i+1})`
      };
      
      recurringEvents.push(newInstance);
    }
    
    console.log(`Generated ${recurringEvents.length} recurring instances for event: ${baseEvent.title}`);
    return recurringEvents;
  };

  // Render custom event content for the calendar
  const renderEventContent = (eventInfo: any) => {
    const icon = eventInfo.event.extendedProps.icon;
    const isRecurring = eventInfo.event.extendedProps.recurrence;
    const hasCustomColor = eventInfo.event.extendedProps.customColor;
    const isCustomEvent = eventInfo.event.extendedProps.type === 'custom';
    
    // Get category information for custom events
    let categoryInfo = null;
    if (isCustomEvent) {
      const originalId = eventInfo.event.id.replace('custom-', '');
      const customEvent = customEvents?.find(e => e.id === originalId);
      if (customEvent?.category) {
        const category = eventCategories.find(c => c.id === customEvent.category);
        if (category) {
          categoryInfo = category;
        }
      }
    }

    return (
      <div 
        className={`fc-event-main-wrapper flex items-center w-full ${isCustomEvent ? 'cursor-move' : ''}`}
        title={isCustomEvent ? (categoryInfo ? `${categoryInfo.label} - Drag to reschedule` : 'Drag to reschedule') : ''}
      >
        {icon && (
          <div className="mr-1">
            {getEventIcon(icon)}
          </div>
        )}
        {categoryInfo && (
          <div className="mr-1 text-xs bg-gray-200 text-gray-700 px-0.5 rounded-sm" title={categoryInfo.label} style={{ fontSize: '0.65rem' }}>
            {categoryInfo.id.substring(0, 2)}
          </div>
        )}
        <div className="flex-grow truncate">
          <span className="truncate">
            {isRecurring && "↻ "}
            {hasCustomColor && "★ "}
            {isCustomEvent && "✎ "}
            {eventInfo.event.title}
          </span>
        </div>
      </div>
    );
  };

  // Simple toast notification function
  const showToast = (message: string) => {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded shadow-lg z-50 animate-fade-in-up';
    toast.textContent = message;
    
    // Add to DOM
    document.body.appendChild(toast);
    
    // Remove after 3 seconds
    setTimeout(() => {
      toast.classList.add('animate-fade-out');
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'credit-card': return <CreditCard className="w-4 h-4" />;
      case 'banknote': return <Banknote className="w-4 h-4" />;
      case 'target': return <Target className="w-4 h-4" />;
      case 'dollar-sign': return <DollarSign className="w-4 h-4" />;
      case 'piggy-bank': return <PiggyBank className="w-4 h-4" />;
      case 'alert-circle': return <AlertCircle className="w-4 h-4" />;
      default: return <CalendarIcon className="w-4 h-4" />;
    }
  };

  // Check for events with reminders and display notifications
  const checkReminders = () => {
    if (!customEvents) return;
    
    const now = new Date();
    const eventsWithReminders = customEvents.filter(event => {
      if (!event.reminder || event.reminderSent) return false;
      
      const eventDate = parseISO(event.start_date);
      const reminderDate = new Date(eventDate);
      
      // Calculate when the reminder should trigger based on the unit
      switch (event.reminderUnit) {
        case 'minutes':
          reminderDate.setMinutes(reminderDate.getMinutes() - (event.reminderTime || 0));
          break;
        case 'hours':
          reminderDate.setHours(reminderDate.getHours() - (event.reminderTime || 0));
          break;
        case 'days':
          reminderDate.setDate(reminderDate.getDate() - (event.reminderTime || 0));
          break;
      }
      
      // Check if it's time to show the reminder (if current time is after reminder time)
      return now >= reminderDate;
    });
    
    // Display notifications for events that need reminders
    eventsWithReminders.forEach(async (event) => {
      // Show notification
      alert(`Reminder: ${event.title} on ${format(parseISO(event.start_date), 'MMM dd, yyyy')}`);
      
      // Mark reminder as sent
      const updatedEvent = { ...event, reminderSent: true };
      await localDataStore.data.updateItem('calendar_events', updatedEvent.id, updatedEvent);
      
      // Update local state
      setCustomEvents(prev => 
        prev.map(e => e.id === event.id ? updatedEvent : e)
      );
    });
  };

  // Filter events based on time horizon
  const getFilteredEvents = () => {
    console.log("Filtering events:", events);
    const today = new Date();
    
    // If there are no events, log this for debugging
    if (events.length === 0) {
      console.log("No events to filter - checking data sources:");
      console.log("Loans:", loans?.length || 0);
      console.log("Goals:", goals?.length || 0);
      console.log("Expenses:", expenses?.length || 0);
      console.log("CDs:", cds?.length || 0);
      console.log("Custom Events:", customEvents?.length || 0);
    }
    
    // Check for old dates and update them to current time period
    const updatedEvents = events.map(event => {
      try {
        // Parse the event date
        const eventDate = new Date(event.start);
        
        // Check if date is valid
        if (isNaN(eventDate.getTime())) {
          console.log("Invalid date for event, using current date:", event.title, event.start);
          // Create a new event with today's date
          return {
            ...event,
            start: format(today, 'yyyy-MM-dd')
          };
        }
        
        // Check if the event is from a past year (before 2024)
        if (eventDate.getFullYear() < 2024) {
          console.log("Updating old event date to current year:", event.title, event.start);
          
          // Create a new date with the same month/day but in the current year
          const updatedDate = new Date(
            today.getFullYear(),
            eventDate.getMonth(),
            eventDate.getDate()
          );
          
          // If the updated date is still in the past, move it forward by a month
          if (updatedDate < today) {
            updatedDate.setMonth(updatedDate.getMonth() + 1);
          }
          
          // Return updated event with new date
          return {
            ...event,
            start: format(updatedDate, 'yyyy-MM-dd')
          };
        }
        
        // If date is already valid and recent, keep it as is
        return event;
      } catch (error) {
        console.error("Error processing event date:", event, error);
        // Return the original event if there's an error
        return event;
      }
    });
    
    // Now filter the updated events
    const filteredEvents = updatedEvents.filter(event => {
      try {
        // Safely parse the date with fallbacks
        let eventDate;
        try {
          eventDate = new Date(event.start);
          // Check if date is valid
          if (isNaN(eventDate.getTime())) {
            console.log("Invalid date for event:", event.title, event.start);
            // Default to today + 30 days if date is invalid
            eventDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
          }
        } catch (dateError) {
          console.error("Error parsing date:", event.start, dateError);
          // Default to today + 30 days if date parsing fails
          eventDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
        }
        
        console.log("Event:", event.title, "Date:", eventDate, "Type:", event.extendedProps.type);

        // Apply type filter if not set to 'all'
        if (eventTypeFilter !== 'all' && event.extendedProps.type !== eventTypeFilter) {
          console.log("Filtering out by type:", event.title, event.extendedProps.type, "filter:", eventTypeFilter);
          return false;
        }
        
        // Apply category filter if not set to 'all'
        if (eventCategoryFilter !== 'all' && event.extendedProps.type === 'custom') {
          // Extract the original event ID (remove the 'custom-' prefix)
          const originalId = event.id.replace('custom-', '');
          
          // Find the original custom event to check its category
          const customEvent = customEvents?.find(e => e.id === originalId);
          
          if (!customEvent?.category || customEvent.category !== eventCategoryFilter) {
            console.log("Filtering out by category:", event.title, customEvent?.category, "filter:", eventCategoryFilter);
            return false;
          }
        }
        
        // Apply search query if provided
        if (searchQuery.trim() !== '') {
          const query = searchQuery.toLowerCase().trim();
          const title = event.title.toLowerCase();
          const description = event.extendedProps.description ? event.extendedProps.description.toLowerCase() : '';
          const amount = event.extendedProps.amount ? event.extendedProps.amount.toString() : '';
          
          // Search in title, description, and amount
          const matchesTitle = title.includes(query);
          const matchesDescription = description.includes(query);
          const matchesAmount = amount.includes(query);
          
          // For custom events, also search in category
          let matchesCategory = false;
          if (event.extendedProps.type === 'custom') {
            const originalId = event.id.replace('custom-', '');
            const customEvent = customEvents?.find(e => e.id === originalId);
            if (customEvent?.category) {
              const category = eventCategories.find(c => c.id === customEvent.category);
              if (category) {
                matchesCategory = category.label.toLowerCase().includes(query);
              }
            }
          }
          
          if (!(matchesTitle || matchesDescription || matchesAmount || matchesCategory)) {
            console.log("Filtering out by search:", event.title);
            return false;
          }
        }

        // Apply time horizon filter - with more lenient handling
        let include = false;
        switch (eventTimeHorizon) {
          case 'week':
            // Events within the next 7 days
            include = eventDate >= today && eventDate <= new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            // Events within the next 30 days
            include = eventDate >= today && eventDate <= new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
            break;
          case 'quarter':
            // Events within the next 90 days
            include = eventDate >= today && eventDate <= new Date(today.getTime() + 90 * 24 * 60 * 60 * 1000);
            break;
          case 'year':
            // Events within the next 365 days
            include = eventDate >= today && eventDate <= new Date(today.getTime() + 365 * 24 * 60 * 60 * 1000);
            break;
          case 'all':
          default:
            include = true;
            break;
        }

        console.log("Time horizon filter:", eventTimeHorizon, "Include:", include, "Event:", event.title);
        return include;
      } catch (error) {
        console.error("Error filtering event:", event, error);
        // Include the event anyway if there's an error (better to show than hide)
        return true;
      }
    });

    console.log("Filtered events:", filteredEvents.length);

    // Deduplicate events before sorting
    const deduplicatedEvents = filteredEvents.reduce<CalendarEvent[]>((unique, event) => {
      // Check if we already have an event with the same title and date
      const isDuplicate = unique.some(existingEvent => {
        try {
          // Compare title and date to identify duplicates
          const sameName = existingEvent.title === event.title;
          const sameDate = existingEvent.start === event.start;
          const sameAmount = existingEvent.extendedProps.amount === event.extendedProps.amount;
          
          // If all key properties match, it's likely a duplicate
          if (sameName && sameDate && sameAmount) {
            console.log("Found duplicate event:", event.title, event.start);
            return true;
          }
          return false;
        } catch (error) {
          console.error("Error checking for duplicate event:", error);
          return false;
        }
      });
      
      // Only add the event if it's not a duplicate
      if (!isDuplicate) {
        unique.push(event);
      }
      
      return unique;
    }, []);
    
    console.log("After deduplication:", deduplicatedEvents.length, "events (removed", 
               filteredEvents.length - deduplicatedEvents.length, "duplicates)");
    
    // Sort by date (closest first)
    return deduplicatedEvents.sort((a, b) => {
      try {
        return new Date(a.start).getTime() - new Date(b.start).getTime();
      } catch (error) {
        console.error("Error sorting events:", error);
        return 0;
      }
    });
  };

  // Get paginated events
  const getPaginatedEvents = () => {
    const filteredEvents = getFilteredEvents();
    const startIndex = (currentPage - 1) * eventsPerPage;
    return {
      events: filteredEvents.slice(startIndex, startIndex + eventsPerPage),
      totalPages: Math.ceil(filteredEvents.length / eventsPerPage),
      totalEvents: filteredEvents.length
    };
  };

  // Group events by month for better organization
  const getGroupedEvents = () => {
    const filteredEvents = getFilteredEvents();
    const groupedEvents: Record<string, CalendarEvent[]> = {};

    filteredEvents.forEach(event => {
      const date = new Date(event.start);
      const monthYear = format(date, 'MMMM yyyy');

      if (!groupedEvents[monthYear]) {
        groupedEvents[monthYear] = [];
      }

      groupedEvents[monthYear].push(event);
    });

    return groupedEvents;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Financial Calendar</h1>
          <p className="text-sm text-gray-500 dark:text-gray-400">Track your financial commitments and goals</p>
        </div>
        <div className="flex gap-2">
          <div className="flex items-center">
            <label htmlFor="forecastMonths" className="mr-2 text-sm text-gray-600 dark:text-gray-300">Show</label>
            <select
              id="forecastMonths"
              value={forecastMonths}
              onChange={(e) => setForecastMonths(Number(e.target.value))}
              className="px-2 py-1 text-sm rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white"
            >
              <option value={3}>3 months</option>
              <option value={6}>6 months</option>
              <option value={12}>1 year</option>
              <option value={24}>2 years</option>
            </select>
          </div>
          
          <div className="flex items-center ml-4">
            <label htmlFor="calendarView" className="mr-2 text-sm text-gray-600 dark:text-gray-300">View</label>
            <select
              id="calendarView"
              value={calendarView}
              onChange={(e) => setCalendarView(e.target.value as any)}
              className="px-2 py-1 text-sm rounded border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white"
            >
              <option value="dayGridMonth">Month</option>
              <option value="dayGridWeek">Week</option>
              <option value="dayGridDay">Day</option>
              <option value="listWeek">Agenda</option>
            </select>
          </div>
          <button
            onClick={() => {
              setSelectedDate(new Date());
              setSelectedEvent(null);
              resetForm();
              setIsModalOpen(true);
            }}
            className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
          >
            <Plus className="w-5 h-5 mr-2" />
            Add Event
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-3 bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
          <FullCalendar
            plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
            initialView={calendarView}
            headerToolbar={{
              left: 'prev,next today',
              center: 'title',
              right: 'dayGridMonth,dayGridWeek,dayGridDay,listWeek'
            }}
            events={events}
            eventContent={renderEventContent}
            dateClick={handleDateClick}
            eventClick={handleEventClick}
            height="auto"
            views={{
              dayGridMonth: { buttonText: 'Month' },
              dayGridWeek: { buttonText: 'Week' },
              dayGridDay: { buttonText: 'Day' },
              listWeek: { buttonText: 'Agenda' }
            }}
            viewDidMount={(viewInfo) => {
              // Update state when view changes from calendar UI
              setCalendarView(viewInfo.view.type as any);
            }}
            editable={true} // Enable drag-and-drop
            eventDrop={handleEventDrop} // Handle event drops
            droppable={true} // Allow dropping on calendar
          />
        </div>

        <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Financial Events</h3>

          <div className="space-y-4">
            <div className="flex flex-col">
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Legend</h4>
              <div className="space-y-2">
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded bg-red-500 mr-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                    <CreditCard className="w-4 h-4 mr-1" /> Loan Payments
                  </span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded bg-blue-500 mr-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                    <Banknote className="w-4 h-4 mr-1" /> CD Maturities
                  </span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded bg-orange-500 mr-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                    <DollarSign className="w-4 h-4 mr-1" /> Expenses
                  </span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded bg-green-500 mr-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                    <DollarSign className="w-4 h-4 mr-1" /> Income
                  </span>
                </div>
                <div className="flex items-center">
                  <div className="w-4 h-4 rounded bg-indigo-500 mr-2"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                    <Target className="w-4 h-4 mr-1" /> Goals
                  </span>
                </div>
              </div>
            </div>

          <div className="border-t pt-4">
            <div className="flex justify-between items-center mb-3">
              <h4 className="font-medium text-gray-700 dark:text-gray-300">Upcoming Events</h4>

              <div className="flex flex-col gap-2">
                {/* Search field */}
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search events..."
                    value={searchQuery}
                    onChange={(e) => {
                      setSearchQuery(e.target.value);
                      setCurrentPage(1); // Reset to first page when search changes
                    }}
                    className="w-full text-xs px-2 py-1 pl-7 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white"
                  />
                  <div className="absolute left-2 top-1.5 text-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  {searchQuery && (
                    <button
                      className="absolute right-2 top-1.5 text-gray-400 hover:text-gray-600"
                      onClick={() => setSearchQuery('')}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  )}
                </div>
                
                <div className="flex gap-2">
                  {/* Time horizon filter */}
                  <select
                    value={eventTimeHorizon}
                    onChange={(e) => {
                      setEventTimeHorizon(e.target.value as any);
                      setCurrentPage(1); // Reset to first page when filter changes
                    }}
                    className="text-xs px-2 py-1 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white flex-1"
                  >
                    <option value="week">Next 7 days</option>
                    <option value="month">Next 30 days</option>
                    <option value="quarter">Next 90 days</option>
                    <option value="year">Next year</option>
                    <option value="all">All future</option>
                  </select>

                  {/* Event type filter */}
                  <select
                    value={eventTypeFilter}
                    onChange={(e) => {
                      setEventTypeFilter(e.target.value);
                      setCurrentPage(1); // Reset to first page when filter changes
                    }}
                    className="text-xs px-2 py-1 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white flex-1"
                  >
                    <option value="all">All types</option>
                    <option value="loan">Loan payments</option>
                    <option value="cd-maturity">CD maturities</option>
                    <option value="goal">Goals</option>
                    <option value="expense">Expenses</option>
                    <option value="custom">Custom events</option>
                  </select>
                </div>

                  {/* Category filter - only shown when custom events are selected */}
                  {(eventTypeFilter === 'custom' || eventTypeFilter === 'all') && (
                    <select
                      value={eventCategoryFilter}
                      onChange={(e) => {
                        setEventCategoryFilter(e.target.value);
                        setCurrentPage(1); // Reset to first page when filter changes
                      }}
                      className="text-xs px-2 py-1 rounded border border-gray-300 focus:outline-none focus:ring-1 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white w-full"
                    >
                      <option value="all">All categories</option>
                      {eventCategories.map(category => (
                        <option key={category.id} value={category.id}>{category.label}</option>
                      ))}
                    </select>
                  )}
                </div>
              </div>

              {/* Event count summary */}
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                {getPaginatedEvents().totalEvents === 0 ? (
                  <p>No events found for the selected filters.</p>
                ) : (
                  <p>Showing {Math.min(eventsPerPage, getPaginatedEvents().totalEvents)} of {getPaginatedEvents().totalEvents} events</p>
                )}
              </div>

              <div className="overflow-y-auto max-h-96">
                {Object.entries(getGroupedEvents()).length > 0 ? (
                  <div className="space-y-4">
                    {Object.entries(getGroupedEvents()).map(([monthYear, monthEvents]) => (
                      <div key={monthYear} className="space-y-2">
                        <h5 className="text-sm font-medium text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-dark-700 px-2 py-1 rounded">
                          {monthYear}
                        </h5>
                        <div className="space-y-2 pl-2">
                          {monthEvents.slice(0, currentPage * eventsPerPage).map(event => (
                            <div
                              key={event.id}
                              className="p-2 rounded border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-dark-700 cursor-pointer"
                              style={{ borderLeftWidth: '4px', borderLeftColor: event.borderColor }}
                              onClick={() => {
                                setSelectedEvent(event);
                                setIsModalOpen(true);
                              }}
                            >
                              <div className="flex justify-between items-start">
                                <div>
                                  <div className="font-medium text-sm flex items-center gap-1">
                                    {event.extendedProps.icon && getEventIcon(event.extendedProps.icon)}
                                    <span>{event.title}</span>
                                  </div>
                                  <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
                                    <span>{format(new Date(event.start), 'EEE, MMM d, yyyy')}</span>
                                    {/* Show days from now for better context */}
                                    {(() => {
                                      const days = Math.ceil((new Date(event.start).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                                      if (days === 0) return <span className="text-green-500 font-medium">(Today)</span>;
                                      if (days === 1) return <span className="text-green-500 font-medium">(Tomorrow)</span>;
                                      if (days < 7) return <span className="text-green-500 font-medium">({days} days)</span>;
                                      if (days < 30) return <span className="text-blue-500 font-medium">({Math.floor(days / 7)} weeks)</span>;
                                      return <span className="text-gray-500 font-medium">({Math.floor(days / 30)} months)</span>;
                                    })()}
                                  </div>
                                </div>
                                {event.extendedProps.amount && (
                                  <div className="text-sm font-semibold">
                                    {formatCurrency(event.extendedProps.amount)}
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}

                    {/* Pagination controls */}
                    {getPaginatedEvents().totalPages > 1 && (
                      <div className="flex justify-center items-center space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                        <button
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                          className="p-1 rounded text-gray-500 dark:text-gray-400 disabled:opacity-50 hover:bg-gray-100 dark:hover:bg-dark-600 flex items-center"
                        >
                          <ChevronLeft className="w-4 h-4 mr-1" />
                          <span className="text-xs">Previous</span>
                        </button>

                        <div className="flex items-center space-x-1">
                          {Array.from({ length: Math.min(5, getPaginatedEvents().totalPages) }, (_, i) => {
                            // Show pages around current page
                            let pageNum;
                            const totalPages = getPaginatedEvents().totalPages;

                            if (totalPages <= 5) {
                              // If 5 or fewer pages, show all
                              pageNum = i + 1;
                            } else if (currentPage <= 3) {
                              // If near start, show first 5 pages
                              pageNum = i + 1;
                            } else if (currentPage >= totalPages - 2) {
                              // If near end, show last 5 pages
                              pageNum = totalPages - 4 + i;
                            } else {
                              // Otherwise show 2 before and 2 after current page
                              pageNum = currentPage - 2 + i;
                            }

                            return (
                              <button
                                key={pageNum}
                                onClick={() => setCurrentPage(pageNum)}
                                className={`w-6 h-6 text-xs flex items-center justify-center rounded ${
                                  currentPage === pageNum
                                    ? 'bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300 font-medium'
                                    : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-dark-600'
                                }`}
                              >
                                {pageNum}
                              </button>
                            );
                          })}
                        </div>

                        <button
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, getPaginatedEvents().totalPages))}
                          disabled={currentPage === getPaginatedEvents().totalPages}
                          className="p-1 rounded text-gray-500 dark:text-gray-400 disabled:opacity-50 hover:bg-gray-100 dark:hover:bg-dark-600 flex items-center"
                        >
                          <span className="text-xs">Next</span>
                          <ChevronRight className="w-4 h-4 ml-1" />
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <p>No events found for the selected filters.</p>
                    <p className="text-sm mt-2">Try adjusting your filters or adding new events.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          resetForm();
        }}
        title={selectedEvent ? "Event Details" : "Add New Event"}
      >
        {selectedEvent ? (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              {selectedEvent.extendedProps.icon && (
                <div className="p-2 rounded-full" style={{ backgroundColor: selectedEvent.backgroundColor }}>
                  {getEventIcon(selectedEvent.extendedProps.icon)}
                </div>
              )}
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">{selectedEvent.title}</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {format(new Date(selectedEvent.start), 'EEEE, MMMM d, yyyy')}
                  {selectedEvent.end && ` - ${format(new Date(selectedEvent.end), 'EEEE, MMMM d, yyyy')}`}
                </p>
              </div>
            </div>

            {selectedEvent.extendedProps.amount && (
              <div className="bg-gray-50 dark:bg-dark-700 p-3 rounded">
                <p className="text-sm text-gray-500 dark:text-gray-400">Amount</p>
                <p className="text-xl font-semibold text-gray-900 dark:text-white">{formatCurrency(selectedEvent.extendedProps.amount)}</p>
              </div>
            )}

            {/* Enhanced loan payment details */}
            {selectedEvent.extendedProps.type === 'loan' && (
              <div className="bg-gray-50 dark:bg-dark-700 p-3 rounded space-y-3">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Principal</p>
                    <p className="text-md font-semibold text-gray-900 dark:text-white">
                      {formatCurrency(selectedEvent.extendedProps.principalPayment || 0)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Interest</p>
                    <p className="text-md font-semibold text-gray-900 dark:text-white">
                      {formatCurrency(selectedEvent.extendedProps.interestPayment || 0)}
                    </p>
                  </div>
                </div>

                <div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">Remaining Balance After Payment</p>
                  <p className="text-md font-semibold text-gray-900 dark:text-white">
                    {formatCurrency(selectedEvent.extendedProps.remainingBalance || 0)}
                  </p>
                </div>

                {selectedEvent.extendedProps.percentPaid !== undefined && (
                  <div className="pt-2">
                    <p className="text-sm text-gray-500 dark:text-gray-400">Loan Progress</p>
                    <div className="w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2.5 mt-1">
                      <div
                        className="bg-green-600 h-2.5 rounded-full"
                        style={{ width: `${Math.min(100, selectedEvent.extendedProps.percentPaid)}%` }}
                      ></div>
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {selectedEvent.extendedProps.percentPaid.toFixed(1)}% paid off
                    </p>
                  </div>
                )}
              </div>
            )}

            {selectedEvent.extendedProps.description && selectedEvent.extendedProps.type !== 'loan' && (
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Description</p>
                <p className="text-gray-900 dark:text-white">{selectedEvent.extendedProps.description}</p>
              </div>
            )}

            {selectedEvent.extendedProps.recurrence && (
              <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm flex items-center text-gray-500 dark:text-gray-400">
                  <span className="mr-1">↻</span> This is a recurring payment
                </p>
              </div>
            )}

            {selectedEvent.extendedProps.type === 'custom' && (
              <div className="flex justify-end mt-4 pt-2 border-t border-gray-200 dark:border-gray-700">
                <button
                  className="px-3 py-1 text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                  Delete Event
                </button>
              </div>
            )}
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="event-title" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Title
              </label>
              <input
                type="text"
                id="event-title"
                value={eventTitle}
                onChange={(e) => setEventTitle(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-600 dark:text-white"
                required
              />
            </div>

            <div>
              <label htmlFor="eventType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Event Type
              </label>
              <select
                id="eventType"
                value={eventType}
                onChange={(e) => setEventType(e.target.value as any)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-600 dark:text-white"
              >
                <option value="expense">Expense</option>
                <option value="income">Income</option>
                <option value="savings">Savings</option>
                <option value="reminder">Reminder</option>
              </select>
            </div>

            <div>
              <label htmlFor="eventCategory" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Category
              </label>
              <select
                id="eventCategory"
                value={eventCategory}
                onChange={(e) => setEventCategory(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-600 dark:text-white"
              >
                <option value="">Select a category (optional)</option>
                {eventCategories.map(category => (
                  <option key={category.id} value={category.id}>{category.label}</option>
                ))}
              </select>
              <p className="text-xs text-gray-500 mt-1">
                Categorizing events helps you organize and filter your financial calendar.
              </p>
            </div>

            <div>
              <label htmlFor="event-amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Amount (optional)
              </label>
              <input
                type="number"
                id="event-amount"
                value={eventAmount}
                onChange={(e) => setEventAmount(e.target.value)}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-600 dark:text-white"
              />
            </div>

            <div>
              <label htmlFor="event-description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description (optional)
              </label>
              <textarea
                id="event-description"
                value={eventDescription}
                onChange={(e) => setEventDescription(e.target.value)}
                rows={3}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-600 dark:text-white"
              />
            </div>

            {/* Recurrence Options */}
            <div className="mb-4">
              <div className="flex items-center mb-2">
                <input
                  type="checkbox"
                  id="isRecurring"
                  checked={isRecurring}
                  onChange={(e) => setIsRecurring(e.target.checked)}
                  className="mr-2"
                />
                <label htmlFor="isRecurring" className="text-sm font-medium text-gray-700">
                  Recurring Event
                </label>
              </div>
              
              {isRecurring && (
                <div className="pl-6 border-l-2 border-gray-200 mt-2">
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Repeat Pattern
                    </label>
                    <select
                      value={recurrencePattern}
                      onChange={(e) => setRecurrencePattern(e.target.value as any)}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                      <option value="yearly">Yearly</option>
                    </select>
                  </div>
                  
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      End Date (Optional)
                    </label>
                    <input
                      type="date"
                      value={recurrenceEndDate}
                      onChange={(e) => setRecurrenceEndDate(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div className="mb-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Number of Occurrences (Optional)
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="24"
                      value={recurrenceCount}
                      onChange={(e) => setRecurrenceCount(e.target.value)}
                      className="w-full p-2 border border-gray-300 rounded-md"
                      placeholder="Leave empty to use end date"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      If both end date and occurrences are specified, the earlier limit will be used.
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Reminder Options */}
            <div className="mb-4">
              <div className="flex items-center mb-2">
                <input
                  type="checkbox"
                  id="reminder"
                  checked={reminder}
                  onChange={(e) => setReminder(e.target.checked)}
                  className="mr-2"
                />
                <label htmlFor="reminder" className="text-sm font-medium text-gray-700">
                  Set Reminder
                </label>
              </div>
              
              {reminder && (
                <div className="pl-6 border-l-2 border-gray-200 mt-2">
                  <div className="flex items-center gap-2">
                    <div className="w-1/3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Remind me
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="60"
                        value={reminderTime}
                        onChange={(e) => setReminderTime(parseInt(e.target.value))}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                    </div>
                    
                    <div className="w-2/3">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Unit
                      </label>
                      <select
                        value={reminderUnit}
                        onChange={(e) => setReminderUnit(e.target.value as any)}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      >
                        <option value="minutes">Minutes before</option>
                        <option value="hours">Hours before</option>
                        <option value="days">Days before</option>
                      </select>
                    </div>
                  </div>
                  
                  <p className="text-xs text-gray-500 mt-2">
                    You will receive a notification {reminderTime} {reminderUnit} before the event.
                  </p>
                </div>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Custom Color (Optional)
              </label>
              <div className="flex items-center">
                <ColorPicker
                  value={eventColor}
                  onChange={setEventColor}
                  colors={[
                    '#3788d8', // Default blue
                    '#28a745', // Green
                    '#dc3545', // Red
                    '#ffc107', // Yellow
                    '#6f42c1', // Purple
                    '#fd7e14', // Orange
                    '#20c997', // Teal
                    '#e83e8c', // Pink
                    '#6c757d', // Gray
                  ]}
                />
                {eventColor && (
                  <button
                    type="button"
                    onClick={() => setEventColor('')}
                    className="ml-2 text-xs text-gray-500 hover:text-gray-700"
                  >
                    Clear
                  </button>
                )}
              </div>
              {eventColor && (
                <div className="mt-2 flex items-center">
                  <div
                    className="w-6 h-6 rounded mr-2"
                    style={{ backgroundColor: eventColor }}
                  ></div>
                  <span className="text-xs text-gray-500">Preview</span>
                </div>
              )}
            </div>

            <div className="flex justify-end">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="mr-3 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:bg-dark-600 dark:text-gray-200 dark:border-dark-500 dark:hover:bg-dark-700"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
            >
              Save Event
            </button>
          </div>
          </form>
        )}
      </Modal>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Financial Calendar</h1>
        <Link
          to="/calendar-tools"
          className="flex items-center px-3 py-1 bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200 transition-colors dark:bg-indigo-900 dark:text-indigo-300 dark:hover:bg-indigo-800"
        >
          <Settings className="w-4 h-4 mr-1" /> Calendar Tools
        </Link>
      </div>
      
      {/* Calendar View */}
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-md p-4 mb-6">
        <FullCalendar
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          initialView="dayGridMonth"
          headerToolbar={{
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,timeGridWeek,timeGridDay'
          }}
          events={getFilteredEvents()}
          eventContent={renderEventContent}
          dateClick={handleDateClick}
          eventClick={handleEventClick}
          editable={true}
          eventDrop={handleEventDrop}
          height="auto"
        />
      </div>
    </div>
  );
}
