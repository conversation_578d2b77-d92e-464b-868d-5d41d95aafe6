/*
  # Create stock tables and policies

  1. New Tables
    - `stocks`
      - Primary table for stock information
      - Includes metadata, price data, and analysis
    - `stock_data`
      - Historical price data for stocks
      - Links to stocks table via stock_id

  2. Security
    - RLS enabled on both tables
    - Policies for authenticated users to manage their own data
    - Automatic updated_at timestamp management
*/

-- Create tables
CREATE TABLE IF NOT EXISTS stocks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users ON DELETE CASCADE,
  symbol text NOT NULL,
  name text NOT NULL,
  price numeric NOT NULL,
  change numeric NOT NULL,
  volume numeric NOT NULL,
  market_cap numeric NOT NULL,
  liquidity_zone jsonb NOT NULL,
  order_blocks jsonb NOT NULL,
  ai_score numeric NOT NULL,
  recommendation text NOT NULL,
  analysis jsonb NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

CREATE TABLE IF NOT EXISTS stock_data (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  stock_id uuid REFERENCES stocks ON DELETE CASCADE,
  date date NOT NULL,
  open numeric NOT NULL,
  high numeric NOT NULL,
  low numeric NOT NULL,
  close numeric NOT NULL,
  volume numeric NOT NULL,
  UNIQUE(stock_id, date)
);

-- Enable RLS
ALTER TABLE stocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_data ENABLE ROW LEVEL SECURITY;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'update_stocks_updated_at'
  ) THEN
    CREATE TRIGGER update_stocks_updated_at
      BEFORE UPDATE ON stocks
      FOR EACH ROW
      EXECUTE FUNCTION update_updated_at();
  END IF;
END $$;

-- Create policies
DO $$
BEGIN
  -- Stocks policies
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'stocks' AND policyname = 'Users can insert own stocks'
  ) THEN
    CREATE POLICY "Users can insert own stocks"
      ON stocks
      FOR INSERT
      TO authenticated
      WITH CHECK (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'stocks' AND policyname = 'Users can read own stocks'
  ) THEN
    CREATE POLICY "Users can read own stocks"
      ON stocks
      FOR SELECT
      TO authenticated
      USING (auth.uid() = user_id);
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'stocks' AND policyname = 'Users can update own stocks'
  ) THEN
    CREATE POLICY "Users can update own stocks"
      ON stocks
      FOR UPDATE
      TO authenticated
      USING (auth.uid() = user_id)
      WITH CHECK (auth.uid() = user_id);
  END IF;

  -- Stock data policies
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'stock_data' AND policyname = 'Users can insert own stock data'
  ) THEN
    CREATE POLICY "Users can insert own stock data"
      ON stock_data
      FOR INSERT
      TO authenticated
      WITH CHECK (EXISTS (
        SELECT 1 FROM stocks
        WHERE stocks.id = stock_data.stock_id
        AND stocks.user_id = auth.uid()
      ));
  END IF;

  IF NOT EXISTS (
    SELECT 1 FROM pg_policies WHERE tablename = 'stock_data' AND policyname = 'Users can read own stock data'
  ) THEN
    CREATE POLICY "Users can read own stock data"
      ON stock_data
      FOR SELECT
      TO authenticated
      USING (EXISTS (
        SELECT 1 FROM stocks
        WHERE stocks.id = stock_data.stock_id
        AND stocks.user_id = auth.uid()
      ));
  END IF;
END $$;