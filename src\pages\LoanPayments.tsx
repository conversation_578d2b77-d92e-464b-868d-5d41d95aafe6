import React, { useState } from 'react';
import { PlusCircle, Trash2, Pencil } from 'lucide-react';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import type { Loan, BankAccount, LoanPayment } from '../types/loan-payments';
import { formatCurrency } from '../utils/currency';
import { format, addMonths, parseISO } from 'date-fns';
import { Modal } from '../components/Modal';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { useAuthStore } from '../store/localAuthStore';
import { localDataStore } from '../lib/local-data-store';

function calculateTotalMonthlyPayment(loans: Loan[]): number {
  return loans?.reduce((sum, loan) => sum + loan.monthly_payment, 0) || 0;
}

function calculateCoverageMonths(accountBalance: number, monthlyPayment: number): number {
  if (!monthlyPayment) return 0;
  return Math.floor(accountBalance / monthlyPayment);
}

function formatDateSafely(dateString: string): string {
  try {
    const date = parseISO(dateString);
    return format(date, 'MMM d, yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return 'Invalid Date';
  }
}

export function LoanPayments() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<LoanPayment | null>(null);
  const [selectedLoanId, setSelectedLoanId] = useState('');
  const [paymentAmount, setPaymentAmount] = useState('');
  const [paymentDate, setPaymentDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const [isEditing, setIsEditing] = useState(false);
  const user = useAuthStore((state) => state.user);

  const { data: loans } = useSupabaseQuery<Loan>('loans');
  const { data: bankAccounts } = useSupabaseQuery<BankAccount>('bank_accounts');
  const { data: payments, refetch: refetchPayments } = useSupabaseQuery<LoanPayment>('loan_payments', {
    orderBy: { column: 'payment_date', ascending: false }
  });

  const enbdAccount = bankAccounts?.find(account =>
    account.bank_name.toLowerCase() === 'enbd' &&
    account.account_type === 'investment'
  );
  const totalMonthlyPayment = calculateTotalMonthlyPayment(loans || []);
  const coverageMonths = enbdAccount ? calculateCoverageMonths(enbdAccount.balance, totalMonthlyPayment) : 0;
  const coverageEndDate = addMonths(new Date(), coverageMonths);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (isEditing && selectedPayment) {
        const updatedPayment: LoanPayment = {
          ...selectedPayment,
          loan_id: selectedLoanId,
          amount: Number(paymentAmount),
          payment_date: paymentDate
        };

        // Use the updateItem method from useSupabaseQuery
        await localDataStore.data.updateItem('loan_payments', selectedPayment.id, updatedPayment);
      } else {
        const newPayment: LoanPayment = {
          id: localDataStore.utils.generateId(),
          user_id: user?.id || 'default-user',
          loan_id: selectedLoanId,
          amount: Number(paymentAmount),
          payment_date: paymentDate,
          created_at: new Date().toISOString()
        };

        // Use the addItem method from useSupabaseQuery
        await localDataStore.data.addItem('loan_payments', newPayment);

        // Update bank account balance if selected
        if (enbdAccount) {
          const updatedAccount: BankAccount = {
            ...enbdAccount,
            balance: enbdAccount.balance - Number(paymentAmount)
          };
          await localDataStore.data.updateItem('bank_accounts', enbdAccount.id, updatedAccount);
        }
      }

      setIsModalOpen(false);
      resetForm();
      refetchPayments();
    } catch (error) {
      console.error('Error saving payment:', error);
    }
  };

  const handleEdit = (payment: LoanPayment) => {
    setSelectedPayment(payment);
    setSelectedLoanId(payment.loan_id);
    setPaymentAmount(payment.amount.toString());
    setPaymentDate(format(parseISO(payment.payment_date), 'yyyy-MM-dd'));
    setIsEditing(true);
    setIsModalOpen(true);
  };

  const handleDelete = (payment: LoanPayment) => {
    setSelectedPayment(payment);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedPayment && enbdAccount) {
      try {
        // Use removeItem from localDataStore instead of Supabase
        await localDataStore.data.removeItem('loan_payments', selectedPayment.id);

        // Update bank account balance
        const updatedAccount: BankAccount = {
          ...enbdAccount,
          balance: enbdAccount.balance + selectedPayment.amount
        };
        await localDataStore.data.updateItem('bank_accounts', enbdAccount.id, updatedAccount);

        setIsDeleteModalOpen(false);
        refetchPayments();
      } catch (error) {
        console.error('Error deleting payment:', error);
      }
    }
  };

  const resetForm = () => {
    setSelectedPayment(null);
    setSelectedLoanId('');
    setPaymentAmount('');
    setPaymentDate(format(new Date(), 'yyyy-MM-dd'));
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Loan Payments Overview</h1>
        <button
          onClick={() => {
            resetForm();
            setIsModalOpen(true);
          }}
          className="flex items-center px-4 py-2 bg-indigo-600 dark:bg-indigo-500 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600"
        >
          <PlusCircle className="w-5 h-5 mr-2" />
          Add Payment
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Total Monthly Payment</h3>
          <p className="mt-2 text-3xl font-bold text-rose-600 dark:text-rose-400">
            {formatCurrency(totalMonthlyPayment)}
          </p>
          <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">Combined for all loans</p>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">ENBD Investment Balance</h3>
          <p className="mt-2 text-3xl font-bold text-indigo-600 dark:text-indigo-400">
            {formatCurrency(enbdAccount?.balance || 0)}
          </p>
          <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">Available for payments</p>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Payment Coverage</h3>
          <p className="mt-2 text-3xl font-bold text-emerald-600 dark:text-emerald-400">{coverageMonths} months</p>
          <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">
            Covered until {format(coverageEndDate, 'MMM d, yyyy')}
          </p>
        </div>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          resetForm();
        }}
        title={isEditing ? "Edit Payment" : "Add New Payment"}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="loan" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Select Loan
            </label>
            <select
              id="loan"
              value={selectedLoanId}
              onChange={(e) => setSelectedLoanId(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              required
            >
              <option value="">Select a loan</option>
              {loans?.map(loan => (
                <option key={loan.id} value={loan.id}>
                  {loan.name} - {formatCurrency(loan.monthly_payment)}/month
                </option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Payment Amount
            </label>
            <input
              type="number"
              id="amount"
              value={paymentAmount}
              onChange={(e) => setPaymentAmount(e.target.value)}
              min="0"
              step="0.01"
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              required
            />
          </div>

          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Payment Date
            </label>
            <input
              type="date"
              id="date"
              value={paymentDate}
              onChange={(e) => setPaymentDate(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-gray-600 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              required
            />
          </div>

          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={() => {
                setIsModalOpen(false);
                resetForm();
              }}
              className="px-4 py-2 border rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-indigo-600 dark:bg-indigo-500 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600"
              disabled={!isEditing && !enbdAccount}
            >
              {isEditing ? "Save Changes" : "Add Payment"}
            </button>
          </div>
        </form>
      </Modal>

      <ConfirmDialog
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Payment"
        message="Are you sure you want to delete this payment? The amount will be refunded to your ENBD account."
      />

      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Payment History</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead className="bg-gray-100 dark:bg-dark-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Loan
                </th>
                <th className="px-6 py-3 text-right text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-right text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-600">
              {payments?.map(payment => {
                const loan = loans?.find(l => l.id === payment.loan_id);
                return (
                  <tr key={payment.id} className="hover:bg-gray-50 dark:hover:bg-gray-600">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-gray-200">
                      {formatDateSafely(payment.payment_date)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-800 dark:text-gray-200">
                      {loan?.name || 'Unknown Loan'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-800 dark:text-gray-200">
                      {formatCurrency(payment.amount)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleEdit(payment)}
                          className="p-1 text-gray-600 dark:text-gray-400 hover:text-indigo-600 dark:hover:text-indigo-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600"
                        >
                          <Pencil className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(payment)}
                          className="p-1 text-gray-600 dark:text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-full hover:bg-gray-100 dark:hover:bg-gray-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
              {(!payments || payments.length === 0) && (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-gray-700 dark:text-gray-300">
                    No payments found. Use the "Add Payment" button to make a loan payment.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {!enbdAccount && (
        <div className="bg-yellow-50 dark:bg-yellow-900 border-l-4 border-yellow-400 dark:border-yellow-600 p-4">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                No ENBD investment account found. Please add your ENBD investment account to track payment coverage.
              </p>
              <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                <p>To add an ENBD investment account, go to the Bank Accounts page and create a new account with:</p>
                <ul className="mt-2 list-disc list-inside">
                  <li>Bank Name: ENBD</li>
                  <li>Account Type: Investment</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}