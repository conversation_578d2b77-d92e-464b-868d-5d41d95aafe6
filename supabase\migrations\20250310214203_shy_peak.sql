/*
  # Update users table RLS policy

  1. Changes
    - Add policy to allow authenticated users to upsert their own records
    - Keep existing policy for reading own data

  2. Security
    - Users can only upsert their own records (where id matches auth.uid())
    - Maintains existing read-only policy
*/

CREATE POLICY "Users can upsert own data"
  ON users
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own data"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);