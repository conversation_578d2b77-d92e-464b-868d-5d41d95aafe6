import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '../types/index';
import { useAuthStore } from '../store/localAuthStore';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signOut: () => Promise<void>;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [error, setError] = useState<string | null>(null);

  // Use the local auth store
  const user = useAuthStore((state) => state.user);
  const loading = useAuthStore((state) => state.loading);
  const signOutFromStore = useAuthStore((state) => state.signOut);
  const autoLogin = useAuthStore((state) => state.autoLogin);

  useEffect(() => {
    // Auto-login if no user is present
    if (!user && !loading) {
      console.log('AuthContext: Auto-logging in user');
      autoLogin();
    }
  }, [user, loading, autoLogin]);

  const signOut = async () => {
    try {
      await signOutFromStore();
      setError(null);
    } catch (err) {
      console.error('Error signing out:', err);
      setError(err instanceof Error ? err.message : 'Sign out failed');
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, signOut, error }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};