
def financial_risk_forecast():
    # 1. Inputs
    monthly_income = 173000
    monthly_loans = 198000
    monthly_expenses = 30000
    savings = 612000

    # 2. Education payments (schedule by month)
    education_schedule = {
        'Sep': 210828 + 32425,  # University + School
        'Nov': 50549 + 33175,
        'Dec': 105389,
        'Jan': 54890
    }

    # 3. Initialize result tracking
    month_order = ['Sep', 'Oct', 'Nov', 'Dec', 'Jan']
    monthly_deficit_log = []
    total_deficit = 0
    education_reserved = 0

    # 4. Reserve education fees
    for m in education_schedule:
        education_reserved += education_schedule[m]
    remaining_savings = savings - education_reserved

    # 5. Forecast for 5 months (Sep to Jan)
    for month in month_order:
        # Education payment in this month
        edu_payment = education_schedule.get(month, 0)

        # Monthly deficit = income - (loans + expenses + edu)
        total_outflow = monthly_loans + monthly_expenses + edu_payment
        net_balance = monthly_income - total_outflow
        total_deficit += -net_balance if net_balance < 0 else 0
        remaining_savings += net_balance  # If negative, savings will reduce

        monthly_deficit_log.append({
            'month': month,
            'income': monthly_income,
            'loans': monthly_loans,
            'expenses': monthly_expenses,
            'education_payment': edu_payment,
            'net_cashflow': net_balance,
            'remaining_savings': remaining_savings
        })

    # 6. Assess risk level
    risk_level = "High" if remaining_savings < 0 else "Medium" if remaining_savings < 100000 else "Low"

    # 7. Output analysis
    return {
        "education_reserved": education_reserved,
        "initial_remaining_savings": savings - education_reserved,
        "monthly_forecast": monthly_deficit_log,
        "final_savings": remaining_savings,
        "risk_level": risk_level,
        "advice": generate_advice(remaining_savings, total_deficit)
    }


def generate_advice(final_savings, total_deficit):
    advice = []

    if final_savings < 0:
        advice.append("⚠️ Your savings will be fully depleted. Urgent action needed.")
    elif final_savings < 100000:
        advice.append("⚠️ Your financial buffer is low. Consider reducing expenses or restructuring loans.")

    advice.append("👉 Suggested Actions:")
    advice.append("- Negotiate with bank to lower monthly loan payments below 143,000 EGP.")
    advice.append("- Increase monthly income by at least 55,000 EGP.")
    advice.append("- Explore part-time or freelance income options.")
    advice.append("- Delay non-essential expenses until financial situation improves.")
    return advice
