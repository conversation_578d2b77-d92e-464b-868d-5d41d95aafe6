{"name": "financial-advisor", "private": true, "version": "1.0.0", "main": "electron.js", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron:build": "vite build && electron-builder", "package": "electron-builder --win --x64"}, "dependencies": {"@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@supabase/supabase-js": "2.39.7", "chart.js": "4.4.1", "date-fns": "3.3.1", "jspdf": "2.5.1", "jspdf-autotable": "3.8.2", "lucide-react": "0.294.0", "plaid": "18.1.0", "react": "18.3.1", "react-chartjs-2": "5.2.0", "react-dom": "18.3.1", "react-router-dom": "6.22.2", "recharts": "^2.15.3", "xlsx": "0.18.5", "zod": "3.22.4", "zustand": "4.5.1"}, "devDependencies": {"@eslint/js": "8.56.0", "@types/react": "18.3.5", "@types/react-dom": "18.3.0", "@vitejs/plugin-react": "4.2.1", "autoprefixer": "10.4.18", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^35.1.3", "electron-builder": "^26.0.12", "electron-squirrel-startup": "^1.0.1", "eslint": "8.56.0", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.5", "globals": "15.9.0", "postcss": "8.4.35", "tailwindcss": "3.4.1", "typescript": "5.3.3", "typescript-eslint": "7.0.2", "vite": "5.1.4", "wait-on": "^8.0.3"}, "build": {"appId": "com.financial.advisor", "productName": "Financial Advisor", "directories": {"output": "electron-dist"}, "files": ["dist/**/*", "electron.js"], "win": {"target": "nsis", "icon": "public/favicon.ico"}}}