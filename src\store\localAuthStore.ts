import { create } from 'zustand';
import { User, localDataStore } from '../lib/local-data-store';

/**
 * Auth store interface
 */
interface AuthState {
  user: User | null;
  loading: boolean;
  initialized: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  initialize: () => Promise<void>;
  autoLogin: () => Promise<void>;
}

/**
 * Create the auth store
 */
export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  loading: true,
  initialized: false,
  
  initialize: async () => {
    // Skip if already initialized
    if (get().initialized) return;
    
    console.log('Initializing auth store...');
    
    try {
      // Get current user
      const user = await localDataStore.auth.getCurrentUser();
      
      set({ 
        user: user, 
        loading: false,
        initialized: true
      });
      
      // Listen for auth state changes
      localDataStore.auth.onAuthStateChange((event, user) => {
        console.log('Auth state changed:', event, user?.email);
        set({ user: user || null });
      });
      
    } catch (error) {
      console.error('Error initializing auth:', error);
      set({ 
        user: null, 
        loading: false,
        initialized: true
      });
    }
  },
  
  signIn: async (email: string, password: string) => {
    console.log('Signing in user:', email);
    
    // Ensure auth is initialized
    if (!get().initialized) {
      await get().initialize();
    }
    
    try {
      // Sign in with local data store
      const user = await localDataStore.auth.signIn(email, password);
      set({ user });
      console.log('User signed in successfully:', user.email);
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  },
  
  signUp: async (email: string, password: string) => {
    console.log('Signing up new user:', email);
    
    // Ensure auth is initialized
    if (!get().initialized) {
      await get().initialize();
    }
    
    try {
      // Sign up with local data store
      const user = await localDataStore.auth.signUp(email, password);
      set({ user });
      console.log('User signed up successfully:', user.email);
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    }
  },
  
  signOut: async () => {
    console.log('Signing out user');
    
    // Ensure auth is initialized
    if (!get().initialized) {
      await get().initialize();
    }
    
    try {
      // Sign out with local data store
      await localDataStore.auth.signOut();
      set({ user: null });
      console.log('User signed out successfully');
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  },
  
  autoLogin: async () => {
    try {
      set({ loading: true });
      const user = await localDataStore.auth.autoLogin();
      set({ user, loading: false });
    } catch (error) {
      console.error('Auto-login error:', error);
      set({ loading: false });
    }
  }
}));

// Initialize auth state when the store is created
// We use try-catch to handle any initialization errors gracefully
(async () => {
  try {
    await useAuthStore.getState().initialize();
  } catch (error) {
    console.error('Failed to initialize auth store:', error);
  }
})();
