import React from 'react';
import { Stock } from '../types';
import { 
  TrendingUp, TrendingDown, Target, Brain, 
  AlertTriangle, Scale, ArrowUpRight, ShieldCheck 
} from 'lucide-react';

interface AnalysisDashboardProps {
  stocks: Stock[];
}

export const AnalysisDashboard: React.FC<AnalysisDashboardProps> = ({ stocks }) => {
  const stock = stocks[0]; // For now, we'll focus on one stock

  const getConfidenceColor = (score: number) => {
    if (score >= 7) return 'text-green-600';
    if (score >= 5) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getDirectionIcon = (direction: 'up' | 'down') => {
    return direction === 'up' ? (
      <TrendingUp className="h-5 w-5 text-green-600" />
    ) : (
      <TrendingDown className="h-5 w-5 text-red-600" />
    );
  };

  return (
    <div className="mt-8 space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">Current Price</h3>
            {stock.change >= 0 ? (
              <TrendingUp className="h-6 w-6 text-green-600" />
            ) : (
              <TrendingDown className="h-6 w-6 text-red-600" />
            )}
          </div>
          <p className="text-3xl font-bold text-gray-900">{stock.price.toFixed(2)} EGP</p>
          <p className={`mt-2 ${stock.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {stock.change >= 0 ? '+' : ''}{stock.change}%
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">AI Confidence</h3>
            <Brain className="h-6 w-6 text-blue-600" />
          </div>
          <p className={`text-3xl font-bold ${getConfidenceColor(stock.aiScore)}`}>
            {stock.aiScore.toFixed(1)}/10
          </p>
          <p className="mt-2 text-gray-600">Based on multiple factors</p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">Risk Level</h3>
            <AlertTriangle className="h-6 w-6 text-orange-600" />
          </div>
          <p className="text-3xl font-bold text-orange-600">
            {stock.analysis.riskAnalysis.confidence > 0.7 ? 'Low' : 
             stock.analysis.riskAnalysis.confidence > 0.5 ? 'Medium' : 'High'}
          </p>
          <p className="mt-2 text-gray-600">
            {(stock.analysis.riskAnalysis.confidence * 100).toFixed(0)}% confidence
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">Recommendation</h3>
            <Target className="h-6 w-6 text-purple-600" />
          </div>
          <p className={`text-3xl font-bold ${
            stock.recommendation === 'Buy' ? 'text-green-600' :
            stock.recommendation === 'Sell' ? 'text-red-600' :
            'text-yellow-600'
          }`}>
            {stock.recommendation}
          </p>
          <p className="mt-2 text-gray-600">Based on analysis</p>
        </div>
      </div>

      {/* Technical Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center mb-6">
            <Brain className="h-6 w-6 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">AI Predictions</h3>
          </div>
          <div className="space-y-4">
            {(['shortTerm', 'midTerm', 'longTerm'] as const).map((term) => (
              <div key={term} className="flex items-center justify-between">
                <span className="text-gray-700">
                  {term.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                </span>
                <div className="flex items-center space-x-4">
                  {getDirectionIcon(stock.analysis.predictions[term].direction)}
                  <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className={`h-full rounded-full ${
                        stock.analysis.predictions[term].probability > 0.7 ? 'bg-green-500' :
                        stock.analysis.predictions[term].probability > 0.5 ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}
                      style={{ width: `${stock.analysis.predictions[term].probability * 100}%` }}
                    />
                  </div>
                  <span className="text-gray-900 font-medium w-12">
                    {(stock.analysis.predictions[term].probability * 100).toFixed(0)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center mb-6">
            <Scale className="h-6 w-6 text-blue-600 mr-2" />
            <h3 className="text-lg font-semibold text-gray-800">Risk Management</h3>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-red-50 rounded-lg">
              <p className="text-sm text-red-600 font-medium">Stop Loss</p>
              <p className="text-xl font-bold text-red-700 mt-1">
                {stock.analysis.riskAnalysis.stopLoss.toFixed(2)} EGP
              </p>
            </div>
            <div className="p-4 bg-green-50 rounded-lg">
              <p className="text-sm text-green-600 font-medium">Take Profit</p>
              <p className="text-xl font-bold text-green-700 mt-1">
                {stock.analysis.riskAnalysis.takeProfit.toFixed(2)} EGP
              </p>
            </div>
          </div>
          <div className="mt-4 space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Risk/Reward Ratio</span>
              <span className="font-medium text-gray-900">{stock.analysis.riskAnalysis.riskRewardRatio}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Position Size</span>
              <span className="font-medium text-gray-900">{stock.analysis.riskAnalysis.positionSize}%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Support & Resistance */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center mb-6">
          <ShieldCheck className="h-6 w-6 text-blue-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-800">Key Levels</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600 font-medium">Support</p>
            <p className="text-xl font-bold text-gray-900 mt-1">
              {stock.liquidityZone.support.toFixed(2)} EGP
            </p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600 font-medium">Resistance</p>
            <p className="text-xl font-bold text-gray-900 mt-1">
              {stock.liquidityZone.resistance.toFixed(2)} EGP
            </p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600 font-medium">Bullish OB</p>
            <p className="text-xl font-bold text-green-600 mt-1">
              {stock.orderBlocks.bullish[0].toFixed(2)} EGP
            </p>
          </div>
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600 font-medium">Bearish OB</p>
            <p className="text-xl font-bold text-red-600 mt-1">
              {stock.orderBlocks.bearish[0].toFixed(2)} EGP
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};