import * as React from 'react';

interface ColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  label?: string;
  colors?: string[];
}

export function ColorPicker({ 
  value, 
  onChange, 
  label = 'Color', 
  colors = [
    '#ef4444', // red (loan)
    '#0ea5e9', // blue (cd)
    '#f97316', // orange (expense)
    '#22c55e', // green (income)
    '#6366f1', // indigo (goal)
    '#a855f7', // purple (reminder)
    '#6b7280', // gray (default)
    '#0891b2', // cyan
    '#84cc16', // lime
    '#eab308', // yellow
    '#ec4899', // pink
    '#14b8a6', // teal
  ] 
}: ColorPickerProps) {
  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
      )}
      <div className="flex flex-wrap gap-2">
        {colors.map((color) => (
          <button
            key={color}
            type="button"
            className={`w-8 h-8 rounded-full border-2 ${value === color ? 'border-gray-900 dark:border-white' : 'border-transparent'}`}
            style={{ backgroundColor: color }}
            onClick={() => onChange(color)}
            aria-label={`Select ${color} color`}
          />
        ))}
      </div>
    </div>
  );
}
