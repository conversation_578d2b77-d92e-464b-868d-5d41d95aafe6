import { create } from 'zustand';
import { User } from '../types/index';
import { supabase } from '../lib/supabase';

interface AuthState {
  user: User | null;
  loading: boolean;
  initialized: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  initialize: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  loading: true,
  initialized: false,
  initialize: async () => {
    // Skip if already initialized
    if (get().initialized) return;
    
    console.log('Initializing auth store...');
    
    try {
      // Wait for storage to initialize first
      await supabase.storage.initialize();
      console.log('Storage initialized for auth');
      
      // Get session data if there is an active session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.user) {
        console.log('Found existing user session', session.user.email);
        set({ user: session.user, loading: false, initialized: true });
      } else {
        console.log('No user session found');
        set({ user: null, loading: false, initialized: true });
      }

      // Listen for auth state changes
      supabase.auth.onAuthStateChange((_event, session) => {
        console.log('Auth state changed', _event, session?.user?.email);
        set({ user: session?.user || null });
      });
    } catch (error) {
      console.error('Error initializing auth:', error);
      set({ user: null, loading: false, initialized: true });
    }
  },
  signIn: async (email: string, password: string) => {
    console.log('Signing in user', email);
    // Ensure auth is initialized
    if (!get().initialized) {
      await get().initialize();
    }
    
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        console.error('Sign in error:', error);
        throw error;
      }
      
      if (data?.user) {
        console.log('User signed in successfully', data.user.email);
        set({ user: data.user });
      }
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    }
  },
  signUp: async (email: string, password: string) => {
    console.log('Signing up new user', email);
    // Ensure auth is initialized
    if (!get().initialized) {
      await get().initialize();
    }
    
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      
      if (error) {
        console.error('Sign up error:', error);
        throw error;
      }
      
      if (data?.user) {
        console.log('User signed up successfully', data.user.email);
        set({ user: data.user });
      }
    } catch (error) {
      console.error('Error signing up:', error);
      throw error;
    }
  },
  signOut: async () => {
    console.log('Signing out user');
    // Ensure auth is initialized
    if (!get().initialized) {
      await get().initialize();
    }
    
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Sign out error:', error);
        throw error;
      }
      console.log('User signed out successfully');
      set({ user: null });
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  },
}));

// Initialize auth state when the store is created
// We use try-catch to handle any initialization errors gracefully
(async () => {
  try {
    await useAuthStore.getState().initialize();
  } catch (error) {
    console.error('Failed to initialize auth store:', error);
  }
})();