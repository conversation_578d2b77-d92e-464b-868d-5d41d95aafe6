/*
  # Add calendar events table

  1. New Tables
    - `calendar_events`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `title` (text)
      - `start_date` (date)
      - `end_date` (date, optional)
      - `type` (text)
      - `amount` (numeric, optional)
      - `description` (text, optional)
      - `created_at` (timestamp with time zone)

  2. Security
    - Enable RLS on `calendar_events` table
    - Add policy for authenticated users to manage their own events
*/

CREATE TABLE IF NOT EXISTS calendar_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  title text NOT NULL,
  start_date date NOT NULL,
  end_date date,
  type text NOT NULL,
  amount numeric,
  description text,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE calendar_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own events"
  ON calendar_events
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);