import { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Calendar,
  BookOpen,
  Calculator,
  Target,
  Shield,
  ChevronRight
} from 'lucide-react';
import { formatCurrency } from '../utils/currency';
import {
  financialRiskForecast,
  createDefaultEducationSchedule,
  getRiskColor,
  type ForecastInputs,
  type FinancialForecastResult,
  type EducationPayment
} from '../lib/financialForecast';
import { localDataStore } from '../lib/local-data-store';

export function FinancialForecast() {
  const [inputs, setInputs] = useState<ForecastInputs>({
    monthly_income: 173000,
    monthly_loans: 198048.25, // Default loan payment for Sep-Nov
    monthly_expenses: 30000,
    current_savings: 612000,
    education_schedule: createDefaultEducationSchedule(),
    loan_schedule: [
      { month: 'Dec', amount: 126244 }, // Reduced payment starting December
      { month: 'Jan', amount: 126244 }  // Continues in January
    ],
    forecast_months: ['Sep', 'Oct', 'Nov', 'Dec', 'Jan']
  });

  const [forecast, setForecast] = useState<FinancialForecastResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Load actual data and saved forecast settings from local storage
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load saved forecast settings first
        const latestSettings = await localDataStore.data.loadData('forecast_settings', 'forecast_settings_1');

        // Load loans to calculate monthly payments
        const loans = await localDataStore.data.loadCollection('loans') || [];
        const totalMonthlyLoans = loans.reduce((sum: number, loan: any) => sum + (loan.monthly_payment || 0), 0);

        // Load expenses to calculate monthly expenses
        const expenses = await localDataStore.data.loadCollection('expenses') || [];
        const currentMonth = new Date().getMonth();
        const monthlyExpenses = expenses
          .filter((expense: any) => new Date(expense.date).getMonth() === currentMonth)
          .reduce((sum: number, expense: any) => sum + (expense.amount || 0), 0);

        // Load bank accounts to estimate savings
        const bankAccounts = await localDataStore.data.loadCollection('bank_accounts') || [];
        const totalSavings = bankAccounts.reduce((sum: number, account: any) => sum + (account.balance || 0), 0);

        // Update inputs with saved settings or actual data
        setInputs(prev => ({
          ...prev,
          monthly_income: latestSettings?.monthly_income || prev.monthly_income,
          monthly_loans: latestSettings?.monthly_loans || (totalMonthlyLoans > 0 ? totalMonthlyLoans : prev.monthly_loans),
          monthly_expenses: latestSettings?.monthly_expenses || (monthlyExpenses > 0 ? monthlyExpenses : prev.monthly_expenses),
          current_savings: latestSettings?.current_savings || (totalSavings > 0 ? totalSavings : prev.current_savings),
          education_schedule: latestSettings?.education_schedule || prev.education_schedule,
          loan_schedule: latestSettings?.loan_schedule || prev.loan_schedule
        }));
      } catch (error) {
        console.error('Error loading financial data:', error);
      }
    };

    loadData();
  }, []);

  // Save forecast settings whenever inputs change
  useEffect(() => {
    const saveForecastSettings = async () => {
      try {
        console.log('Attempting to save forecast settings...', inputs);
        const settingsToSave = {
          id: 'forecast_settings_1',
          user_id: 'default-user',
          monthly_income: inputs.monthly_income,
          monthly_loans: inputs.monthly_loans,
          monthly_expenses: inputs.monthly_expenses,
          current_savings: inputs.current_savings,
          education_schedule: inputs.education_schedule,
          loan_schedule: inputs.loan_schedule,
          forecast_months: inputs.forecast_months,
          updated_at: new Date().toISOString()
        };

        console.log('Settings to save:', settingsToSave);

        // Save to local storage
        const result = await localDataStore.data.updateItem('forecast_settings', 'forecast_settings_1', settingsToSave);
        console.log('Forecast settings saved successfully:', result);
      } catch (error) {
        console.error('Error saving forecast settings:', error);
      }
    };

    // Skip saving on initial load (when inputs are default values)
    if (inputs.monthly_income !== 173000 || inputs.education_schedule.length !== 4) {
      // Debounce the save operation to avoid too frequent saves
      const timeoutId = setTimeout(saveForecastSettings, 1000);
      return () => clearTimeout(timeoutId);
    }
  }, [inputs]);

  // Calculate forecast when inputs change
  useEffect(() => {
    setIsLoading(true);
    try {
      const result = financialRiskForecast(inputs);
      setForecast(result);
    } catch (error) {
      console.error('Error calculating forecast:', error);
    } finally {
      setIsLoading(false);
    }
  }, [inputs]);

  const updateEducationPayment = (index: number, field: keyof EducationPayment, value: string | number) => {
    const newSchedule = [...inputs.education_schedule];
    newSchedule[index] = { ...newSchedule[index], [field]: value };
    setInputs(prev => ({ ...prev, education_schedule: newSchedule }));
  };

  const addEducationPayment = () => {
    setInputs(prev => ({
      ...prev,
      education_schedule: [...prev.education_schedule, { month: 'Feb', amount: 0, description: '' }]
    }));
  };

  const removeEducationPayment = (index: number) => {
    setInputs(prev => ({
      ...prev,
      education_schedule: prev.education_schedule.filter((_, i) => i !== index)
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
        <div className="flex items-center space-x-3">
          <Calculator className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Financial Risk Forecast</h1>
            <p className="text-gray-600 dark:text-gray-300">Analyze your financial position and cash flow projections</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Input Panel */}
        <div className="lg:col-span-1 space-y-6">
          {/* Basic Inputs */}
          <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Financial Inputs</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Monthly Income
                </label>
                <input
                  type="number"
                  value={inputs.monthly_income}
                  onChange={(e) => setInputs(prev => ({ ...prev, monthly_income: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Monthly Loan Payments
                </label>
                <input
                  type="number"
                  value={inputs.monthly_loans}
                  onChange={(e) => setInputs(prev => ({ ...prev, monthly_loans: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Monthly Expenses
                </label>
                <input
                  type="number"
                  value={inputs.monthly_expenses}
                  onChange={(e) => setInputs(prev => ({ ...prev, monthly_expenses: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Current Savings
                </label>
                <input
                  type="number"
                  value={inputs.current_savings}
                  onChange={(e) => setInputs(prev => ({ ...prev, current_savings: Number(e.target.value) }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-dark-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-dark-700 dark:text-white"
                />
              </div>
            </div>
          </div>

          {/* Education Schedule */}
          <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Education Payments</h2>
              <button
                onClick={addEducationPayment}
                className="px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 text-sm"
              >
                Add Payment
              </button>
            </div>
            <div className="space-y-3">
              {inputs.education_schedule.map((payment, index) => (
                <div key={index} className="border border-gray-200 dark:border-dark-600 rounded-md p-3">
                  <div className="grid grid-cols-2 gap-2 mb-2">
                    <input
                      type="text"
                      placeholder="Month"
                      value={payment.month}
                      onChange={(e) => updateEducationPayment(index, 'month', e.target.value)}
                      className="px-2 py-1 border border-gray-300 dark:border-dark-600 rounded text-sm dark:bg-dark-700 dark:text-white"
                    />
                    <input
                      type="number"
                      placeholder="Amount"
                      value={payment.amount}
                      onChange={(e) => updateEducationPayment(index, 'amount', Number(e.target.value))}
                      className="px-2 py-1 border border-gray-300 dark:border-dark-600 rounded text-sm dark:bg-dark-700 dark:text-white"
                    />
                  </div>
                  <input
                    type="text"
                    placeholder="Description (optional)"
                    value={payment.description || ''}
                    onChange={(e) => updateEducationPayment(index, 'description', e.target.value)}
                    className="w-full px-2 py-1 border border-gray-300 dark:border-dark-600 rounded text-sm dark:bg-dark-700 dark:text-white"
                  />
                  {inputs.education_schedule.length > 1 && (
                    <button
                      onClick={() => removeEducationPayment(index)}
                      className="mt-2 text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Results Panel */}
        <div className="lg:col-span-2 space-y-6">
          {forecast && (
            <>
              {/* Risk Assessment */}
              <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Risk Assessment</h2>
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskColor(forecast.risk_level)}`}>
                    {forecast.risk_level} Risk
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {formatCurrency(inputs.current_savings)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Initial Savings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {formatCurrency(forecast.education_reserved)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Education Reserved</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {formatCurrency(forecast.initial_remaining_savings)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Available Savings</div>
                  </div>
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${forecast.final_savings >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(forecast.final_savings)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-300">Final Savings</div>
                  </div>
                </div>

                {/* Monthly Cash Flow Summary */}
                <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4 mb-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-3">Monthly Cash Flow Breakdown</h3>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-green-600">
                        {formatCurrency(inputs.monthly_income)}
                      </div>
                      <div className="text-gray-600 dark:text-gray-300">Monthly Income</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-red-600">
                        -{formatCurrency(inputs.monthly_loans)}
                      </div>
                      <div className="text-gray-600 dark:text-gray-300">Loan Payments</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-orange-600">
                        -{formatCurrency(inputs.monthly_expenses)}
                      </div>
                      <div className="text-gray-600 dark:text-gray-300">Monthly Expenses</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-semibold ${(inputs.monthly_income - inputs.monthly_loans - inputs.monthly_expenses) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(inputs.monthly_income - inputs.monthly_loans - inputs.monthly_expenses)}
                      </div>
                      <div className="text-gray-600 dark:text-gray-300">Base Net Flow</div>
                    </div>
                  </div>
                </div>

                {/* Advice */}
                <div className="bg-gray-50 dark:bg-dark-700 rounded-lg p-4">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">Financial Advice</h3>
                  <div className="space-y-1">
                    {forecast.advice.map((advice, index) => (
                      <div key={index} className="text-sm text-gray-700 dark:text-gray-300">
                        {advice}
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Monthly Forecast */}
              <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Monthly Forecast</h2>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-dark-600">
                        <th className="text-left py-2 text-gray-900 dark:text-white">Month</th>
                        <th className="text-right py-2 text-gray-900 dark:text-white">Income</th>
                        <th className="text-right py-2 text-gray-900 dark:text-white">Loans</th>
                        <th className="text-right py-2 text-gray-900 dark:text-white">Expenses</th>
                        <th className="text-right py-2 text-gray-900 dark:text-white">Education</th>
                        <th className="text-right py-2 text-gray-900 dark:text-white">Net Flow</th>
                        <th className="text-right py-2 text-gray-900 dark:text-white">Savings</th>
                      </tr>
                    </thead>
                    <tbody>
                      {forecast.monthly_forecast.map((month, index) => {
                        const totalOutflow = month.loans + month.expenses + month.education_payment;
                        return (
                          <tr key={index} className="border-b border-gray-100 dark:border-dark-700">
                            <td className="py-2 font-medium text-gray-900 dark:text-white">{month.month}</td>
                            <td className="py-2 text-right text-green-600">{formatCurrency(month.income)}</td>
                            <td className="py-2 text-right text-red-600">-{formatCurrency(month.loans)}</td>
                            <td className="py-2 text-right text-orange-600">-{formatCurrency(month.expenses)}</td>
                            <td className="py-2 text-right text-blue-600">
                              {month.education_payment > 0 ? `-${formatCurrency(month.education_payment)}` : '-'}
                            </td>
                            <td className={`py-2 text-right font-medium ${month.net_cashflow >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {month.net_cashflow >= 0 ? '+' : ''}{formatCurrency(month.net_cashflow)}
                            </td>
                            <td className={`py-2 text-right font-medium ${month.remaining_savings >= 100000 ? 'text-green-600' : month.remaining_savings >= 0 ? 'text-yellow-600' : 'text-red-600'}`}>
                              {formatCurrency(month.remaining_savings)}
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
