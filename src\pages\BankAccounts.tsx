import React, { useState, useEffect } from 'react';
import { Plus<PERSON>ir<PERSON>, Pencil, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import { Modal } from '../components/Modal';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { useAuthStore } from '../store/localAuthStore';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { formatCurrency } from '../utils/currency';
import { Transaction } from '../types/index'; // Import Transaction

// Define BankAccount interface locally to avoid import errors
interface BankAccount {
  id: string;
  user_id?: string;
  bank_name: string;
  account_type: string;
  account_number: string;
  account_name?: string;
  routing_number?: string;
  balance: number;
  created_at: string;
}

export function BankAccounts() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<BankAccount | null>(null);
  const [bankName, setBankName] = useState('');
  const [accountName, setAccountName] = useState('');
  const [accountNumber, setAccountNumber] = useState('');
  const [routingNumber, setRoutingNumber] = useState('');
  const [balance, setBalance] = useState('');
  const [accountType, setAccountType] = useState<'checking' | 'savings' | 'investment'>('checking');
  const user = useAuthStore((state) => state.user);

  const [recentTransactions, setRecentTransactions] = useState<Record<string, Transaction[]>>({});

  const { data: accounts, loading, deleteItem, updateItem, addItem } = useSupabaseQuery<BankAccount>('bank_accounts', {
    orderBy: { column: 'created_at', ascending: false }
  });

  useEffect(() => {
    if (accounts?.length) {
      const mockTransactions: Record<string, Transaction[]> = {};

      accounts.forEach(account => {
        if (account.account_type.toLowerCase() === 'checking') {
          // Only create mock transactions for checking accounts
          mockTransactions[account.id] = [
            {
              id: 'mock-transaction-1', // Added mock id
              description: 'Direct Deposit',
              date: format(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), 'MMM d, yyyy'),
              amount: 2500
            },
            {
              id: 'mock-transaction-2', // Added mock id
              description: 'Coffee Shop',
              date: format(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), 'MMM d, yyyy'),
              amount: -4.50
            },
            {
              id: 'mock-transaction-3', // Added mock id
              description: 'Grocery Store',
              date: format(new Date(), 'MMM d, yyyy'),
              amount: -56.78
            }
          ];
        }
      });

      setRecentTransactions(mockTransactions);
    }
  }, [accounts]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (selectedAccount) {
        await updateItem(selectedAccount.id, {
          bank_name: bankName,
          account_name: accountName,
          account_number: accountNumber,
          routing_number: routingNumber,
          balance: Number(balance),
          account_type: accountType
        });
      } else {
        await addItem({
          user_id: user?.id!,
          bank_name: bankName,
          account_name: accountName,
          account_number: accountNumber,
          routing_number: routingNumber,
          balance: Number(balance),
          account_type: accountType
        });
      }

      setIsModalOpen(false);
      resetForm();
    } catch (error) {
      console.error('Error saving bank account:', error);
    }
  };

  const handleEdit = (account: BankAccount) => {
    setSelectedAccount(account);
    setBankName(account.bank_name);
    setAccountName(account.account_name || '');
    setAccountNumber(account.account_number);
    setRoutingNumber(account.routing_number || '');
    setBalance(account.balance.toString());
    setAccountType(account.account_type as 'checking' | 'savings' | 'investment');
    setIsModalOpen(true);
  };

  const handleDelete = (account: BankAccount) => {
    setSelectedAccount(account);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedAccount) {
      try {
        await deleteItem(selectedAccount.id);
        setIsDeleteModalOpen(false);
      } catch (error) {
        console.error('Error deleting bank account:', error);
      }
    }
  };

  const resetForm = () => {
    setSelectedAccount(null);
    setBankName('');
    setAccountName('');
    setAccountNumber('');
    setRoutingNumber('');
    setBalance('');
    setAccountType('checking');
  };

  const totalBalance = accounts?.reduce((sum, account) => sum + account.balance, 0) || 0;

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Bank Accounts</h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Manage your banking relationships and track balances
          </p>
        </div>
        <button
          onClick={() => {
            resetForm();
            setIsModalOpen(true);
          }}
          className="flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors shadow-sm"
        >
          <PlusCircle className="w-5 h-5 mr-2" />
          Add Account
        </button>
      </div>

      {/* Total Balance Summary */}
      <div className="bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 p-6 rounded-lg shadow-sm text-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-blue-100 font-medium text-sm mb-2">Total Balance</h3>
            <p className="text-3xl font-bold">{formatCurrency(totalBalance)}</p>
            <p className="text-blue-200 text-sm mt-1">Across {accounts?.length || 0} account{accounts?.length !== 1 ? 's' : ''}</p>
          </div>
          <div className="p-3 bg-blue-400/20 rounded-lg">
            <PlusCircle className="h-8 w-8 text-blue-100" />
          </div>
        </div>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          resetForm();
        }}
        title={selectedAccount ? "Edit Bank Account" : "Add New Bank Account"}
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="bankName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Bank Name
              </label>
              <input
                type="text"
                id="bankName"
                value={bankName}
                onChange={(e) => setBankName(e.target.value)}
                className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                placeholder="e.g., Chase Bank"
                required
              />
            </div>

            <div>
              <label htmlFor="accountName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Account Name
              </label>
              <input
                type="text"
                id="accountName"
                value={accountName}
                onChange={(e) => setAccountName(e.target.value)}
                className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                placeholder="e.g., Primary Checking"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="accountNumber" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Account Number
              </label>
              <input
                type="text"
                id="accountNumber"
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
                className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                placeholder="Account number"
                required
              />
            </div>

            <div>
              <label htmlFor="routingNumber" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Routing Number
              </label>
              <input
                type="text"
                id="routingNumber"
                value={routingNumber}
                onChange={(e) => setRoutingNumber(e.target.value)}
                className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                placeholder="Routing number (optional)"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="balance" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Current Balance
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">$</span>
                <input
                  type="number"
                  id="balance"
                  value={balance}
                  onChange={(e) => setBalance(e.target.value)}
                  min="0"
                  step="0.01"
                  className="block w-full pl-8 rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
                  placeholder="0.00"
                  required
                />
              </div>
            </div>

            <div>
              <label htmlFor="accountType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Account Type
              </label>
              <select
                id="accountType"
                value={accountType}
                onChange={(e) => setAccountType(e.target.value as 'checking' | 'savings' | 'investment')}
                className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:focus:border-blue-400 dark:focus:ring-blue-400"
              >
                <option value="checking">💳 Checking</option>
                <option value="savings">💰 Savings</option>
                <option value="investment">📈 Investment</option>
              </select>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-600">
            <button
              type="button"
              onClick={() => {
                setIsModalOpen(false);
                resetForm();
              }}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors shadow-sm"
            >
              {selectedAccount ? "Save Changes" : "Add Account"}
            </button>
          </div>
        </form>
      </Modal>

      <ConfirmDialog
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Bank Account"
        message="Are you sure you want to delete this bank account? This action cannot be undone."
      />

      {/* Account Cards */}
      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {accounts?.map((account) => {
          const getAccountIcon = (type: string) => {
            switch (type.toLowerCase()) {
              case 'checking': return '💳';
              case 'savings': return '💰';
              case 'investment': return '📈';
              default: return '🏦';
            }
          };

          const getAccountColor = (type: string) => {
            switch (type.toLowerCase()) {
              case 'checking': return 'blue';
              case 'savings': return 'emerald';
              case 'investment': return 'purple';
              default: return 'gray';
            }
          };

          const color = getAccountColor(account.account_type);

          return (
            <div key={account.id} className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <div className={`p-2 bg-${color}-50 dark:bg-${color}-900/20 rounded-lg`}>
                    <span className="text-xl">{getAccountIcon(account.account_type)}</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{account.bank_name}</h3>
                    <p className={`text-sm font-medium capitalize text-${color}-600 dark:text-${color}-400`}>
                      {account.account_type}
                    </p>
                  </div>
                </div>
                <div className="flex gap-1">
                  <button
                    onClick={() => handleEdit(account)}
                    className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Pencil className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(account)}
                    className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Account Details */}
              <div className={`p-4 bg-${color}-50 dark:bg-${color}-900/10 rounded-lg mb-4`}>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Current Balance</span>
                  <span className={`text-2xl font-bold text-${color}-600 dark:text-${color}-400`}>
                    {formatCurrency(account.balance)}
                  </span>
                </div>
                {account.account_name && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{account.account_name}</p>
                )}
              </div>

              {/* Account Info */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500 dark:text-gray-400">Account Number:</span>
                  <span className="font-mono text-gray-700 dark:text-gray-300">
                    ****{account.account_number.slice(-4)}
                  </span>
                </div>
                {account.routing_number && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500 dark:text-gray-400">Routing Number:</span>
                    <span className="font-mono text-gray-700 dark:text-gray-300">
                      {account.routing_number}
                    </span>
                  </div>
                )}
              </div>

              {/* Recent Activity for Checking Accounts */}
              {account.account_type.toLowerCase() === 'checking' && (
                <div className="pt-4 border-t border-gray-200 dark:border-gray-600">
                  <h4 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3 flex items-center gap-2">
                    📊 Recent Activity
                  </h4>
                  <div className="space-y-2">
                    {recentTransactions[account.id] ? (
                      recentTransactions[account.id].map((transaction, idx) => (
                        <div key={idx} className="flex justify-between items-center py-2 border-b border-gray-100 dark:border-gray-700 last:border-0">
                          <div>
                            <p className="font-medium text-gray-800 dark:text-gray-200 text-sm">{transaction.description}</p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">{transaction.date}</p>
                          </div>
                          <span className={`font-semibold text-sm ${
                            transaction.amount > 0
                              ? 'text-emerald-600 dark:text-emerald-400'
                              : 'text-red-600 dark:text-red-400'
                          }`}>
                            {transaction.amount > 0 ? '+' : ''}{formatCurrency(transaction.amount)}
                          </span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4">
                        <p className="text-gray-500 dark:text-gray-400 text-sm">No recent transactions</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Empty State */}
      {accounts?.length === 0 && (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
            <PlusCircle className="h-12 w-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No bank accounts yet</h3>
          <p className="text-gray-500 dark:text-gray-400 mb-6">Get started by adding your first bank account</p>
          <button
            onClick={() => {
              resetForm();
              setIsModalOpen(true);
            }}
            className="inline-flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
          >
            <PlusCircle className="w-5 h-5 mr-2" />
            Add Your First Account
          </button>
        </div>
      )}
    </div>
  );
}
