import { useState, useEffect } from 'react';
import { Calendar, Clock, DollarSign, TrendingUp, AlertCircle } from 'lucide-react';
import { format, addMonths, addDays, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isSameDay, parseISO } from 'date-fns';
import { useAuthStore } from '../store/localAuthStore';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { formatCurrency } from '../utils/currency';

interface FinancialEvent {
  id: string;
  date: Date;
  title: string;
  type: 'loan_payment' | 'cd_maturity' | 'income' | 'milestone' | 'reminder';
  amount?: number;
  description: string;
  priority: 'high' | 'medium' | 'low';
  status: 'upcoming' | 'due_today' | 'overdue' | 'completed';
}

export function FinancialCalendar() {
  const { user } = useAuthStore();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [_selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [events, setEvents] = useState<FinancialEvent[]>([]);
  const [viewMode, setViewMode] = useState<'month' | 'list'>('month');

  // Fetch data
  const { data: loans } = useSupabaseQuery('loans', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  const { data: cds } = useSupabaseQuery('cds', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Generate financial events
  useEffect(() => {
    generateFinancialEvents();
  }, [loans, cds, currentDate]);

  const generateFinancialEvents = () => {
    const generatedEvents: FinancialEvent[] = [];
    const today = new Date();
    const sixMonthsFromNow = addMonths(today, 6);

    // Generate loan payment events
    if (loans) {
      loans.forEach((loan: any) => {
        let paymentDate = new Date(loan.next_payment_date);
        let eventCount = 0;

        while (paymentDate <= sixMonthsFromNow && eventCount < 12) {
          const isOverdue = paymentDate < today;
          const isDueToday = isSameDay(paymentDate, today);

          generatedEvents.push({
            id: `loan-${loan.id}-${eventCount}`,
            date: new Date(paymentDate),
            title: `${loan.name} Payment`,
            type: 'loan_payment',
            amount: loan.monthly_payment,
            description: `Monthly payment for ${loan.name}`,
            priority: 'high',
            status: isOverdue ? 'overdue' : isDueToday ? 'due_today' : 'upcoming'
          });

          paymentDate = addMonths(paymentDate, 1);
          eventCount++;
        }
      });
    }

    // Generate CD maturity events
    if (cds) {
      cds.forEach((cd: any) => {
        try {
          const maturityDate = parseISO(cd.maturity_date);
          if (maturityDate <= sixMonthsFromNow) {
            const isOverdue = maturityDate < today;
            const isDueToday = isSameDay(maturityDate, today);

            generatedEvents.push({
              id: `cd-${cd.id}`,
              date: maturityDate,
              title: `${cd.bank_name} CD Matures`,
              type: 'cd_maturity',
              amount: cd.principal_amount,
              description: `CD matures - plan reinvestment strategy`,
              priority: 'medium',
              status: isOverdue ? 'overdue' : isDueToday ? 'due_today' : 'upcoming'
            });
          }
        } catch (error) {
          console.error('Error parsing CD maturity date:', error);
        }
      });
    }

    // Generate monthly income events (CD income)
    let incomeDate = startOfMonth(today);
    for (let i = 0; i < 6; i++) {
      generatedEvents.push({
        id: `income-${i}`,
        date: new Date(incomeDate),
        title: 'CD Income',
        type: 'income',
        amount: 118333, // Monthly CD income
        description: 'Monthly CD interest income (excluding CIB)',
        priority: 'medium',
        status: isSameDay(incomeDate, today) ? 'due_today' : 'upcoming'
      });
      incomeDate = addMonths(incomeDate, 1);
    }

    // Generate milestone events
    const debtFreeDate = addMonths(today, 24); // Estimated based on projections
    generatedEvents.push({
      id: 'debt-free-milestone',
      date: debtFreeDate,
      title: 'Projected Debt-Free Date',
      type: 'milestone',
      description: 'All loans projected to be paid off',
      priority: 'low',
      status: 'upcoming'
    });

    // Sort events by date
    generatedEvents.sort((a, b) => a.date.getTime() - b.date.getTime());
    setEvents(generatedEvents);
  };

  // Get events for a specific date
  const getEventsForDate = (date: Date) => {
    return events.filter(event => isSameDay(event.date, date));
  };

  // Get events for current month
  const getCurrentMonthEvents = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    return events.filter(event =>
      event.date >= monthStart && event.date <= monthEnd
    );
  };

  // Get upcoming events (next 30 days)
  const getUpcomingEvents = () => {
    const today = new Date();
    const thirtyDaysFromNow = addDays(today, 30);
    return events.filter(event =>
      event.date >= today && event.date <= thirtyDaysFromNow
    ).slice(0, 10); // Limit to 10 events
  };

  // Calendar grid generation
  const generateCalendarDays = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const calendarStart = addDays(monthStart, -monthStart.getDay());
    const calendarEnd = addDays(monthEnd, 6 - monthEnd.getDay());

    return eachDayOfInterval({ start: calendarStart, end: calendarEnd });
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'loan_payment': return <DollarSign className="w-3 h-3" />;
      case 'cd_maturity': return <TrendingUp className="w-3 h-3" />;
      case 'income': return <TrendingUp className="w-3 h-3" />;
      case 'milestone': return <Calendar className="w-3 h-3" />;
      default: return <Clock className="w-3 h-3" />;
    }
  };

  const getEventColor = (event: FinancialEvent) => {
    if (event.status === 'overdue') return 'bg-red-500';
    if (event.status === 'due_today') return 'bg-orange-500';

    switch (event.type) {
      case 'loan_payment': return 'bg-rose-500';
      case 'cd_maturity': return 'bg-blue-500';
      case 'income': return 'bg-emerald-500';
      case 'milestone': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Financial Calendar</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => setViewMode('month')}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              viewMode === 'month'
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
            }`}
          >
            Month View
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              viewMode === 'list'
                ? 'bg-indigo-600 text-white'
                : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
            }`}
          >
            List View
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-dark-800 p-4 rounded-lg shadow-sm">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Overdue</p>
              <p className="text-lg font-bold text-red-600">
                {events.filter(e => e.status === 'overdue').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 p-4 rounded-lg shadow-sm">
          <div className="flex items-center">
            <Clock className="w-5 h-5 text-orange-500 mr-2" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Due Today</p>
              <p className="text-lg font-bold text-orange-600">
                {events.filter(e => e.status === 'due_today').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 p-4 rounded-lg shadow-sm">
          <div className="flex items-center">
            <Calendar className="w-5 h-5 text-blue-500 mr-2" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">This Month</p>
              <p className="text-lg font-bold text-blue-600">
                {getCurrentMonthEvents().length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 p-4 rounded-lg shadow-sm">
          <div className="flex items-center">
            <TrendingUp className="w-5 h-5 text-emerald-500 mr-2" />
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Next 30 Days</p>
              <p className="text-lg font-bold text-emerald-600">
                {getUpcomingEvents().length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {viewMode === 'month' ? (
        // Month View
        <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {format(currentDate, 'MMMM yyyy')}
            </h2>
            <div className="flex space-x-2">
              <button
                onClick={() => setCurrentDate(addMonths(currentDate, -1))}
                className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md text-sm"
              >
                Previous
              </button>
              <button
                onClick={() => setCurrentDate(new Date())}
                className="px-3 py-1 bg-indigo-600 text-white rounded-md text-sm"
              >
                Today
              </button>
              <button
                onClick={() => setCurrentDate(addMonths(currentDate, 1))}
                className="px-3 py-1 bg-gray-200 dark:bg-gray-700 rounded-md text-sm"
              >
                Next
              </button>
            </div>
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="p-2 text-center text-sm font-medium text-gray-500 dark:text-gray-400">
                {day}
              </div>
            ))}

            {generateCalendarDays().map(day => {
              const dayEvents = getEventsForDate(day);
              const isCurrentMonth = isSameMonth(day, currentDate);
              const isToday = isSameDay(day, new Date());

              return (
                <div
                  key={day.toISOString()}
                  className={`min-h-[80px] p-1 border border-gray-200 dark:border-gray-600 ${
                    isCurrentMonth ? 'bg-white dark:bg-dark-800' : 'bg-gray-50 dark:bg-gray-700'
                  } ${isToday ? 'ring-2 ring-indigo-500' : ''}`}
                  onClick={() => setSelectedDate(day)}
                >
                  <div className={`text-sm ${
                    isCurrentMonth ? 'text-gray-900 dark:text-white' : 'text-gray-400'
                  } ${isToday ? 'font-bold' : ''}`}>
                    {format(day, 'd')}
                  </div>

                  <div className="space-y-1 mt-1">
                    {dayEvents.slice(0, 2).map(event => (
                      <div
                        key={event.id}
                        className={`text-xs p-1 rounded text-white ${getEventColor(event)}`}
                        title={event.description}
                      >
                        <div className="flex items-center">
                          {getEventIcon(event.type)}
                          <span className="ml-1 truncate">{event.title}</span>
                        </div>
                      </div>
                    ))}
                    {dayEvents.length > 2 && (
                      <div className="text-xs text-gray-500">
                        +{dayEvents.length - 2} more
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ) : (
        // List View
        <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Upcoming Events</h2>
          </div>

          <div className="divide-y divide-gray-200 dark:divide-gray-600">
            {getUpcomingEvents().map(event => (
              <div key={event.id} className="p-6 hover:bg-gray-50 dark:hover:bg-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full text-white ${getEventColor(event)}`}>
                      {getEventIcon(event.type)}
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                        {event.title}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {event.description}
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500">
                        {format(event.date, 'EEEE, MMMM d, yyyy')}
                      </p>
                    </div>
                  </div>

                  <div className="text-right">
                    {event.amount && (
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(event.amount)}
                      </p>
                    )}
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      event.status === 'overdue'
                        ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                        : event.status === 'due_today'
                        ? 'bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100'
                    }`}>
                      {event.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
