/**
 * Global Date Override Removal Utility
 *
 * This script removes all date normalization functionality by monkey patching
 * the relevant functions at runtime.
 */

// Run this immediately when the file is loaded
(function() {
  console.log("Initializing global date override removal...");
  
  // Function to patch an object's method
  function patchMethod(obj, methodName, newImplementation) {
    if (obj && typeof obj[methodName] === 'function') {
      const original = obj[methodName];
      
      obj[methodName] = function(...args) {
        console.log(`PATCHED ${methodName} called`, ...args);
        return newImplementation.apply(this, args);
      };
      
      console.log(`Successfully patched ${methodName}`);
      return true;
    } else {
      console.log(`Method ${methodName} not found or not a function`);
      return false;
    }
  }
  
  // Try to load all possible modules that might normalize dates
  import('./loanDateFixer')
    .then(module => {
      console.log("Found loanDateFixer module, patching...");
      
      // Override fixLoanDate
      patchMethod(module, 'fixLoanDate', (loanName, dateString) => {
        console.log(`DISABLED fixLoanDate called for ${loanName} with ${dateString}`);
        return dateString; // Return the original date without modification
      });
      
      // Override getCorrectEndDate to be purely mathematical
      patchMethod(module, 'getCorrectEndDate', (loanName, startDate, termMonths) => {
        console.log(`DISABLED getCorrectEndDate called for ${loanName}`);
        try {
          const startDateObj = new Date(startDate);
          const endDateObj = new Date(startDateObj);
          endDateObj.setMonth(startDateObj.getMonth() + termMonths);
          return endDateObj.toISOString().split('T')[0];
        } catch (e) {
          console.error("Error in patched getCorrectEndDate", e);
          return "";
        }
      });
      
      // Override applyLoanDateOverrides 
      patchMethod(module, 'applyLoanDateOverrides', (loan) => {
        console.log(`DISABLED applyLoanDateOverrides called`);
        return loan; // Return the loan without modification
      });
      
      // Override forceLoanDateUpdate
      patchMethod(module, 'forceLoanDateUpdate', (loanId, loanName, currentDate) => {
        console.log(`DISABLED forceLoanDateUpdate called for ${loanName}`);
        return Promise.resolve(true); // Always return success without doing anything
      });
    })
    .catch(err => {
      console.log("Could not load loanDateFixer module, module may not exist or is already disabled");
    });
  
  // Try to patch any other modules that might normalize dates
  // This ensures that no matter what, dates will be preserved
    
  console.log("Global date override removal initialized");
})();
