import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';

// Import both auth stores
import { useAuthStore as useSupabaseAuthStore } from '../store/authStore';
import { useAuthStore as useLocalAuthStore } from '../store/localAuthStore';

// Choose the appropriate auth store based on the environment
const useAuthStore = window.USE_LOCAL_STORE 
  ? useLocalAuthStore 
  : useSupabaseAuthStore;

export function Register() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const signUp = useAuthStore((state) => state.signUp);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      console.log('Store type:', window.USE_LOCAL_STORE ? 'Local' : 'Supabase');
      console.log('Attempting to register with:', email);
      
      // Dump debug info about the store to console
      if (window.USE_LOCAL_STORE) {
        console.log('Using local auth store');
        try {
          // @ts-ignore - Access internal methods for debugging
          window.dumpStorage = async () => {
            try {
              // @ts-ignore - Access PyWebView API if available
              const result = await window.pywebview.api.dump_storage();
              console.log('Storage dump:', result);
              return result;
            } catch (dumpErr) {
              console.error('Error dumping storage:', dumpErr);
            }
          };
          
          // Attempt to dump storage
          window.dumpStorage?.();
        } catch (debugErr) {
          console.log('Error setting up debug:', debugErr);
        }
      }
      
      await signUp(email, password);
      console.log('Registration successful');
      navigate('/');
    } catch (err: any) {
      console.error('Registration error details:', err);
      
      // Log more details about the error
      if (err instanceof Error) {
        console.error('Error name:', err.name);
        console.error('Error message:', err.message);
        console.error('Error stack:', err.stack);
      } else {
        console.error('Unknown error type:', typeof err);
        console.error('Error stringified:', JSON.stringify(err));
      }
      
      // Check for specific error messages
      if (err?.message?.includes('already exists')) {
        setError('An account with this email already exists. Please sign in instead.');
      } else {
        setError(`Failed to create account: ${err?.message || 'Unknown error'}. Please try again.`);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Create your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          Already have an account?{' '}
          <Link to="/login" className="font-medium text-indigo-600 hover:text-indigo-500">
            Sign in
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="new-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Create account
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}