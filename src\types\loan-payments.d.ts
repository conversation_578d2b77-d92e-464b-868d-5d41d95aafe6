/**
 * Type definitions for loan payments functionality
 */

import { User } from './index';

export interface Loan {
  id: string;
  user_id: string;
  name: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  remaining_balance: number;
  monthly_payment: number;
  next_payment_date: string;
  created_at: string;
}

export interface LoanPayment {
  id: string;
  user_id: string;
  loan_id: string;
  amount: number;
  payment_date: string;
  created_at: string;
}

export interface BankAccount {
  id: string;
  user_id: string;
  bank_name: string;
  account_name: string;
  account_number: string;
  balance: number;
  account_type: 'checking' | 'savings' | 'investment';
  created_at: string;
}
