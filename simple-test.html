<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PyWebView Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
        }
        button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1d4ed8;
        }
        pre {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PyWebView Test</h1>
        <p>This page tests if PyWebView is working correctly.</p>
        
        <div>
            <h2>Test PyWebView API</h2>
            <button id="testApi">Test API</button>
            <button id="testStorage">Test Storage</button>
            <div id="apiResult" class="result">Results will appear here...</div>
        </div>
    </div>

    <script>
        // Log when the page loads
        console.log('Test page loaded');
        
        // Check if pywebview is available
        if (window.pywebview) {
            console.log('PyWebView is available on page load');
        } else {
            console.log('PyWebView is NOT available on page load');
        }
        
        // Listen for pywebview ready event
        window.addEventListener('pywebviewready', function() {
            console.log('PyWebView ready event fired');
            document.getElementById('apiResult').innerHTML = 'PyWebView ready event fired';
        });
        
        // Test API button
        document.getElementById('testApi').addEventListener('click', function() {
            const resultDiv = document.getElementById('apiResult');
            
            try {
                resultDiv.innerHTML = 'Testing PyWebView API...';
                
                if (window.pywebview && window.pywebview.api) {
                    const apiMethods = Object.keys(window.pywebview.api);
                    resultDiv.innerHTML = `
                        <p>PyWebView API is available!</p>
                        <p>Available methods: ${apiMethods.join(', ')}</p>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <p>PyWebView API is NOT available.</p>
                        <p>window.pywebview: ${window.pywebview ? 'exists' : 'does not exist'}</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        });
        
        // Test Storage button
        document.getElementById('testStorage').addEventListener('click', async function() {
            const resultDiv = document.getElementById('apiResult');
            
            try {
                resultDiv.innerHTML = 'Testing storage...';
                
                if (window.pywebview && window.pywebview.api) {
                    // Test with PyWebView API
                    const testKey = 'test-key-' + Date.now();
                    const testValue = JSON.stringify({ test: 'data', timestamp: Date.now() });
                    
                    const saveResult = await window.pywebview.api.set_item(testKey, testValue);
                    const loadResult = await window.pywebview.api.get_item(testKey);
                    
                    resultDiv.innerHTML = `
                        <p>Storage test with PyWebView API:</p>
                        <p>Save result: ${saveResult}</p>
                        <p>Load result: ${loadResult ? 'Data retrieved successfully' : 'Failed to retrieve data'}</p>
                        <p>Data: ${loadResult}</p>
                    `;
                } else {
                    // Test with localStorage
                    const testKey = 'test-key-' + Date.now();
                    const testValue = JSON.stringify({ test: 'data', timestamp: Date.now() });
                    
                    localStorage.setItem(testKey, testValue);
                    const loadResult = localStorage.getItem(testKey);
                    
                    resultDiv.innerHTML = `
                        <p>Storage test with localStorage:</p>
                        <p>Data saved successfully</p>
                        <p>Load result: ${loadResult ? 'Data retrieved successfully' : 'Failed to retrieve data'}</p>
                        <p>Data: ${loadResult}</p>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
