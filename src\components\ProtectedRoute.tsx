import { Outlet } from 'react-router-dom';
import { useEffect } from 'react';

// Import the local auth store for auto-login
import { useAuthStore as useLocalAuthStore } from '../store/localAuthStore';

export function ProtectedRoute() {
  const user = useLocalAuthStore((state) => state.user);
  const autoLogin = useLocalAuthStore((state) => state.autoLogin);
  
  // Auto-login if no user is found
  useEffect(() => {
    if (!user) {
      console.log('No user found, auto-logging in');
      autoLogin();
    }
  }, [user, autoLogin]);
  
  // Always render the protected content - authentication is bypassed
  return <Outlet />;
}