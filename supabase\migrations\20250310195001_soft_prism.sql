/*
  # Add bank accounts table

  1. New Tables
    - `bank_accounts`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to users)
      - `bank_name` (text)
      - `account_name` (text)
      - `account_number` (text)
      - `balance` (numeric)
      - `account_type` (text) - checking, savings, or investment
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on `bank_accounts` table
    - Add policy for authenticated users to manage their own accounts
*/

CREATE TABLE IF NOT EXISTS bank_accounts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  bank_name text NOT NULL,
  account_name text NOT NULL,
  account_number text NOT NULL,
  balance numeric NOT NULL DEFAULT 0 CHECK (balance >= 0),
  account_type text NOT NULL CHECK (account_type IN ('checking', 'savings', 'investment')),
  created_at timestamptz DEFAULT now()
);

ALTER TABLE bank_accounts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own bank accounts"
  ON bank_accounts
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);