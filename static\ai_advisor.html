<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Financial Analysis Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .ai-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .ai-header {
            text-align: center;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .ai-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .ai-feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            border: 1px solid #e0e0e0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .ai-feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .feature-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .ai-chat-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 10px;
            max-width: 80%;
        }

        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: white;
            border: 1px solid #e0e0e0;
            margin-right: auto;
        }

        .chat-input-container {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
        }

        .send-button {
            padding: 12px 25px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: bold;
        }

        .send-button:hover {
            background: #0056b3;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .analysis-result {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-top: 20px;
            border-left: 5px solid #28a745;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .loading::after {
            content: '';
            animation: dots 1.5s steps(5, end) infinite;
        }

        @keyframes dots {
            0%, 20% { content: '.'; }
            40% { content: '..'; }
            60% { content: '...'; }
            80%, 100% { content: ''; }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <h2>💰 Financial Advisor</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="index.html" class="nav-link">Dashboard</a></li>
                <li class="nav-item"><a href="loans.html" class="nav-link">Loans</a></li>
                <li class="nav-item"><a href="cds.html" class="nav-link">CDs</a></li>
                <li class="nav-item"><a href="bank_accounts.html" class="nav-link">Bank Accounts</a></li>
                <li class="nav-item"><a href="ai_advisor.html" class="nav-link active">📊 Analysis</a></li>
            </ul>
        </div>
    </nav>

    <div class="ai-container">
        <div class="ai-header">
            <h1>📊 Financial Analysis Dashboard</h1>
            <p>Track and analyze your financial data</p>
        </div>

        <div class="ai-features">
            <div class="ai-feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">Expense Analysis</div>
                <div class="feature-description">
                    Review your spending patterns and categorize expenses for better budget management.
                </div>
                <button class="btn btn-primary" onclick="analyzeExpenses()">View Expense Summary</button>
            </div>

            <div class="ai-feature-card">
                <div class="feature-icon">💰</div>
                <div class="feature-title">Loan Management</div>
                <div class="feature-description">
                    Track your loan balances, payments, and progress toward payoff goals.
                </div>
                <button class="btn btn-primary" onclick="optimizeLoans()">View Loan Summary</button>
            </div>

            <div class="ai-feature-card">
                <div class="feature-icon">🏥</div>
                <div class="feature-title">Financial Overview</div>
                <div class="feature-description">
                    Get a comprehensive view of your overall financial status and account balances.
                </div>
                <button class="btn btn-primary" onclick="financialHealthCheck()">View Financial Overview</button>
            </div>
        </div>

        <div class="ai-chat-container">
            <h3>💬 Financial Guidance</h3>
            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    <strong>Notice:</strong> AI functionality has been permanently removed to improve app performance. Use the manual tracking features in the main dashboard to monitor your finances. For advanced analysis, consider consulting with a professional financial advisor.
                </div>
            </div>
            <div class="chat-input-container">
                <input type="text" class="chat-input" id="chatInput" placeholder="AI chat permanently disabled" disabled>
                <button class="send-button" id="sendButton" disabled>Send</button>
            </div>
        </div>

        <div id="analysisResults"></div>
    </div>

    <script src="ai_advisor.js"></script>
</body>
</html>
