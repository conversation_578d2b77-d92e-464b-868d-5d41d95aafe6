/*
  # Enhanced Trading Platform Features

  1. New Tables
    - `watchlists`
      - User watchlists for tracking stocks
    - `alerts`
      - Price and technical alerts
    - `portfolios`
      - Portfolio tracking and management
    - `trades`
      - Trade history and performance
    - `news`
      - Market news and analysis
    - `technical_patterns`
      - Detected chart patterns
    - `correlations`
      - Stock correlation analysis

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Create watchlists table
CREATE TABLE IF NOT EXISTS watchlists (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create watchlist_items table
CREATE TABLE IF NOT EXISTS watchlist_items (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  watchlist_id uuid REFERENCES watchlists(id) ON DELETE CASCADE,
  stock_id uuid REFERENCES stocks(id) ON DELETE CASCADE,
  added_at timestamptz DEFAULT now(),
  notes text,
  UNIQUE(watchlist_id, stock_id)
);

-- Create alerts table
CREATE TABLE IF NOT EXISTS alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  stock_id uuid REFERENCES stocks(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('price', 'technical', 'news', 'volume')),
  condition jsonb NOT NULL,
  active boolean DEFAULT true,
  triggered_at timestamptz,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create portfolios table
CREATE TABLE IF NOT EXISTS portfolios (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  initial_balance numeric NOT NULL,
  current_balance numeric NOT NULL,
  currency text DEFAULT 'EGP',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create trades table
CREATE TABLE IF NOT EXISTS trades (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  portfolio_id uuid REFERENCES portfolios(id) ON DELETE CASCADE,
  stock_id uuid REFERENCES stocks(id) ON DELETE CASCADE,
  type text NOT NULL CHECK (type IN ('buy', 'sell')),
  quantity numeric NOT NULL,
  price numeric NOT NULL,
  fees numeric DEFAULT 0,
  notes text,
  executed_at timestamptz DEFAULT now(),
  created_at timestamptz DEFAULT now()
);

-- Create news table
CREATE TABLE IF NOT EXISTS news (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  content text NOT NULL,
  source text NOT NULL,
  url text,
  published_at timestamptz NOT NULL,
  sentiment numeric,
  created_at timestamptz DEFAULT now()
);

-- Create stock_news table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS stock_news (
  stock_id uuid REFERENCES stocks(id) ON DELETE CASCADE,
  news_id uuid REFERENCES news(id) ON DELETE CASCADE,
  relevance numeric,
  PRIMARY KEY (stock_id, news_id)
);

-- Create technical_patterns table
CREATE TABLE IF NOT EXISTS technical_patterns (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  stock_id uuid REFERENCES stocks(id) ON DELETE CASCADE,
  pattern_type text NOT NULL,
  confidence numeric NOT NULL,
  price_level numeric NOT NULL,
  detected_at timestamptz DEFAULT now(),
  validated boolean,
  validated_at timestamptz,
  created_at timestamptz DEFAULT now()
);

-- Create correlations table
CREATE TABLE IF NOT EXISTS correlations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  stock_id_1 uuid REFERENCES stocks(id) ON DELETE CASCADE,
  stock_id_2 uuid REFERENCES stocks(id) ON DELETE CASCADE,
  correlation numeric NOT NULL,
  period text NOT NULL,
  calculated_at timestamptz DEFAULT now(),
  UNIQUE(stock_id_1, stock_id_2, period)
);

-- Enable RLS
ALTER TABLE watchlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE watchlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE news ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_news ENABLE ROW LEVEL SECURITY;
ALTER TABLE technical_patterns ENABLE ROW LEVEL SECURITY;
ALTER TABLE correlations ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can manage their own watchlists"
  ON watchlists
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their watchlist items"
  ON watchlist_items
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM watchlists
    WHERE watchlists.id = watchlist_items.watchlist_id
    AND watchlists.user_id = auth.uid()
  ))
  WITH CHECK (EXISTS (
    SELECT 1 FROM watchlists
    WHERE watchlists.id = watchlist_items.watchlist_id
    AND watchlists.user_id = auth.uid()
  ));

CREATE POLICY "Users can manage their alerts"
  ON alerts
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their portfolios"
  ON portfolios
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their trades"
  ON trades
  FOR ALL
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM portfolios
    WHERE portfolios.id = trades.portfolio_id
    AND portfolios.user_id = auth.uid()
  ))
  WITH CHECK (EXISTS (
    SELECT 1 FROM portfolios
    WHERE portfolios.id = trades.portfolio_id
    AND portfolios.user_id = auth.uid()
  ));

CREATE POLICY "Everyone can read news"
  ON news
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Everyone can read stock news"
  ON stock_news
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Everyone can read technical patterns"
  ON technical_patterns
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Everyone can read correlations"
  ON correlations
  FOR SELECT
  TO authenticated
  USING (true);

-- Add updated_at triggers
CREATE TRIGGER update_watchlists_updated_at
  BEFORE UPDATE ON watchlists
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_alerts_updated_at
  BEFORE UPDATE ON alerts
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER update_portfolios_updated_at
  BEFORE UPDATE ON portfolios
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();

-- Add indexes for better performance
CREATE INDEX idx_watchlist_items_watchlist_id ON watchlist_items(watchlist_id);
CREATE INDEX idx_alerts_user_id ON alerts(user_id);
CREATE INDEX idx_alerts_stock_id ON alerts(stock_id);
CREATE INDEX idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX idx_trades_portfolio_id ON trades(portfolio_id);
CREATE INDEX idx_trades_stock_id ON trades(stock_id);
CREATE INDEX idx_technical_patterns_stock_id ON technical_patterns(stock_id);
CREATE INDEX idx_correlations_stock_ids ON correlations(stock_id_1, stock_id_2);
CREATE INDEX idx_stock_news_stock_id ON stock_news(stock_id);
CREATE INDEX idx_stock_news_news_id ON stock_news(news_id);