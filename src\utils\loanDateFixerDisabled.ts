/**
 * Loan Timeline Fixer Utility (COMPLETELY DISABLED)
 *
 * All date normalization functions are disabled to ensure dates are preserved exactly as entered.
 * This file overrides all date fixer functionality to ensure user-entered dates are never modified.
 */

// Empty date overrides
export const LOAN_START_DATES: Record<string, string> = {};
export const LOAN_END_DATES: Record<string, string> = {};

/**
 * Pass through the date without modification - GUARANTEED
 */
export function fixLoanDate(_loanName: string, dateString: string): string {
  console.log(`fixLoanDate called - DISABLED: returning original date ${dateString} unchanged`);
  return dateString; // Always return the original date string
}

/**
 * Calculate end date based on start date and term months if needed
 * This function is now purely mathematical and doesn't apply any overrides
 */
export function getCorrectEndDate(_loanName: string, startDate: string, termMonths: number): string {
  try {
    console.log(`getCorrectEndDate called with ${startDate}, ${termMonths} - calculating mathematically`);

    // Just do the math without any overrides
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(startDateObj);
    endDateObj.setMonth(startDateObj.getMonth() + termMonths);

    // Format as YYYY-MM-DD
    const result = endDateObj.toISOString().split('T')[0];
    console.log(`Calculated end date: ${result}`);
    return result;
  } catch (error) {
    console.error('Error calculating end date:', error);
    return '';
  }
}

/**
 * Return the loan without any modification whatsoever
 */
export function applyLoanDateOverrides(loan: any): any {
  console.log(`applyLoanDateOverrides called - DISABLED: returning loan unchanged`, loan);
  return loan; // Always return the original loan object
}

/**
 * No-op function that always returns true
 */
export async function forceLoanDateUpdate(_loanId: string, _loanName: string, _currentDate: string): Promise<boolean> {
  console.log(`forceLoanDateUpdate called - DISABLED: no action taken`);
  return true; // Return success without doing anything
}
