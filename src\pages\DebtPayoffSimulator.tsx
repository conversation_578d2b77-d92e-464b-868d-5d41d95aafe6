import { useState, useEffect } from 'react';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { Loan } from '../types/index';
import { useAuthStore } from '../store/localAuthStore';
import { formatCurrency } from '../utils/currency';
import { addMonths, format } from 'date-fns';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface PaymentStrategy {
  name: string;
  description: string;
  monthlyPayment: number;
  payoffDate: Date;
  totalInterestPaid: number;
  monthsToPayoff: number;
  projections: ProjectionMonth[];
}

interface ProjectionMonth {
  month: number;
  date: Date;
  remainingBalance: number;
  interestPaid: number;
  principalPaid: number;
}

export function DebtPayoffSimulator() {
  const { user } = useAuthStore();
  const [strategies, setStrategies] = useState<PaymentStrategy[]>([]);
  const [additionalPayment, setAdditionalPayment] = useState<number>(0);
  const [isCalculating, setIsCalculating] = useState<boolean>(false);
  const [activeIndex, setActiveIndex] = useState<number>(0);

  // Get all loans
  const { data: loans, loading: loansLoading } = useSupabaseQuery<Loan>('loans', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Calculate payment strategies when loans change or additional payment is updated
  useEffect(() => {
    if (loans && loans.length > 0) {
      calculateStrategies();
    }
  }, [loans, additionalPayment]);

  // Calculate different payment strategies
  const calculateStrategies = () => {
    if (!loans || loans.length === 0) return;

    setIsCalculating(true);

    // Create a copy of loans to work with
    const loansCopy = loans.map(loan => ({ ...loan }));

    // Calculate regular payment strategy (minimum payments)
    const regularStrategy = calculateRegularStrategy(loansCopy, additionalPayment);

    // Calculate snowball strategy (focus on smallest balance)
    const snowballStrategy = calculateSnowballStrategy(loansCopy, additionalPayment);

    // Calculate avalanche strategy (focus on highest interest)
    const avalancheStrategy = calculateAvalancheStrategy(loansCopy, additionalPayment);

    setStrategies([regularStrategy, snowballStrategy, avalancheStrategy]);
    setIsCalculating(false);
  };

  // Regular minimum payment strategy
  const calculateRegularStrategy = (loansCopy: Loan[], additionalPayment: number): PaymentStrategy => {
    // Clone loans to avoid modifying the original data
    const workingLoans = JSON.parse(JSON.stringify(loansCopy));

    let totalMonthlyPayment = workingLoans.reduce((sum: number, loan: Loan) => sum + loan.monthly_payment, 0);
    totalMonthlyPayment += additionalPayment;

    const projections: ProjectionMonth[] = [];
    let month = 0;
    let totalInterestPaid = 0;
    let allPaidOff = false;
    const startDate = new Date();

    // Distribute additional payment proportionally
    const additionalPaymentRatio = additionalPayment / totalMonthlyPayment;

    // Continue until all loans are paid off
    while (!allPaidOff && month < 360) { // Max 30 years (360 months)
      const currentDate = addMonths(startDate, month);
      let remainingBalance = 0;
      let monthlyInterestPaid = 0;
      let monthlyPrincipalPaid = 0;

      // Process each loan
      workingLoans.forEach((loan: any) => {
        if (loan.remaining_balance <= 0) return;

        // Calculate interest and principal for this loan
        const monthlyRate = (loan.interest_rate / 100) / 12;
        const interestPayment = loan.remaining_balance * monthlyRate;

        // Adjusted payment includes proportional additional payment
        const adjustedPayment = loan.monthly_payment * (1 + additionalPaymentRatio);
        const principalPayment = Math.min(adjustedPayment - interestPayment, loan.remaining_balance);

        // Update loan
        loan.remaining_balance -= principalPayment;

        // Update totals
        remainingBalance += Math.max(0, loan.remaining_balance);
        monthlyInterestPaid += interestPayment;
        monthlyPrincipalPaid += principalPayment;
      });

      // Add to projections
      projections.push({
        month: month + 1,
        date: currentDate,
        remainingBalance,
        interestPaid: monthlyInterestPaid,
        principalPaid: monthlyPrincipalPaid
      });

      // Update totals
      totalInterestPaid += monthlyInterestPaid;

      // Check if all loans are paid off
      allPaidOff = workingLoans.every((loan: any) => loan.remaining_balance <= 0);

      month++;
    }

    return {
      name: "Regular Payments",
      description: "Making minimum payments plus additional amount distributed proportionally",
      monthlyPayment: totalMonthlyPayment,
      payoffDate: addMonths(startDate, month - 1),
      totalInterestPaid,
      monthsToPayoff: month,
      projections
    };
  };

  // Snowball strategy (focus on smallest balance first)
  const calculateSnowballStrategy = (loansCopy: Loan[], additionalPayment: number): PaymentStrategy => {
    // Clone loans to avoid modifying the original data
    const workingLoans = JSON.parse(JSON.stringify(loansCopy));

    // Sort loans by remaining balance (smallest first)
    workingLoans.sort((a: Loan, b: Loan) => a.remaining_balance - b.remaining_balance);

    let totalMonthlyPayment = workingLoans.reduce((sum: number, loan: Loan) => sum + loan.monthly_payment, 0);
    totalMonthlyPayment += additionalPayment;

    const projections: ProjectionMonth[] = [];
    let month = 0;
    let totalInterestPaid = 0;
    let allPaidOff = false;
    const startDate = new Date();

    // Continue until all loans are paid off
    while (!allPaidOff && month < 360) { // Max 30 years (360 months)
      const currentDate = addMonths(startDate, month);
      let remainingBalance = 0;
      let monthlyInterestPaid = 0;
      let monthlyPrincipalPaid = 0;
      let extraPayment = additionalPayment;

      // Process each loan
      for (let i = 0; i < workingLoans.length; i++) {
        const loan = workingLoans[i];
        if (loan.remaining_balance <= 0) continue;

        // Calculate interest and principal for this loan
        const monthlyRate = (loan.interest_rate / 100) / 12;
        const interestPayment = loan.remaining_balance * monthlyRate;

        // Regular payment plus any extra for the first loan with remaining balance
        let paymentAmount = loan.monthly_payment;
        if (i === 0 && extraPayment > 0) {
          paymentAmount += extraPayment;
          extraPayment = 0;
        }

        const principalPayment = Math.min(paymentAmount - interestPayment, loan.remaining_balance);

        // Update loan
        loan.remaining_balance -= principalPayment;

        // Update totals
        remainingBalance += Math.max(0, loan.remaining_balance);
        monthlyInterestPaid += interestPayment;
        monthlyPrincipalPaid += principalPayment;

        // If this loan is paid off, redistribute its payment to the next loan
        if (loan.remaining_balance <= 0 && i < workingLoans.length - 1) {
          extraPayment = loan.monthly_payment;

          // Re-sort the remaining loans by balance for next month
          workingLoans.sort((a: Loan, b: Loan) => a.remaining_balance - b.remaining_balance);
        }
      }

      // Add to projections
      projections.push({
        month: month + 1,
        date: currentDate,
        remainingBalance,
        interestPaid: monthlyInterestPaid,
        principalPaid: monthlyPrincipalPaid
      });

      // Update totals
      totalInterestPaid += monthlyInterestPaid;

      // Check if all loans are paid off
      allPaidOff = workingLoans.every((loan: any) => loan.remaining_balance <= 0);

      month++;
    }

    return {
      name: "Debt Snowball",
      description: "Focus on paying off smallest balance first",
      monthlyPayment: totalMonthlyPayment,
      payoffDate: addMonths(startDate, month - 1),
      totalInterestPaid,
      monthsToPayoff: month,
      projections
    };
  };

  // Avalanche strategy (focus on highest interest rate first)
  const calculateAvalancheStrategy = (loansCopy: Loan[], additionalPayment: number): PaymentStrategy => {
    // Clone loans to avoid modifying the original data
    const workingLoans = JSON.parse(JSON.stringify(loansCopy));

    // Sort loans by interest rate (highest first)
    workingLoans.sort((a: Loan, b: Loan) => b.interest_rate - a.interest_rate);

    let totalMonthlyPayment = workingLoans.reduce((sum: number, loan: Loan) => sum + loan.monthly_payment, 0);
    totalMonthlyPayment += additionalPayment;

    const projections: ProjectionMonth[] = [];
    let month = 0;
    let totalInterestPaid = 0;
    let allPaidOff = false;
    const startDate = new Date();

    // Continue until all loans are paid off
    while (!allPaidOff && month < 360) { // Max 30 years (360 months)
      const currentDate = addMonths(startDate, month);
      let remainingBalance = 0;
      let monthlyInterestPaid = 0;
      let monthlyPrincipalPaid = 0;
      let extraPayment = additionalPayment;

      // Process each loan
      for (let i = 0; i < workingLoans.length; i++) {
        const loan = workingLoans[i];
        if (loan.remaining_balance <= 0) continue;

        // Calculate interest and principal for this loan
        const monthlyRate = (loan.interest_rate / 100) / 12;
        const interestPayment = loan.remaining_balance * monthlyRate;

        // Regular payment plus any extra for the first loan with remaining balance
        let paymentAmount = loan.monthly_payment;
        if (i === 0 && extraPayment > 0) {
          paymentAmount += extraPayment;
          extraPayment = 0;
        }

        const principalPayment = Math.min(paymentAmount - interestPayment, loan.remaining_balance);

        // Update loan
        loan.remaining_balance -= principalPayment;

        // Update totals
        remainingBalance += Math.max(0, loan.remaining_balance);
        monthlyInterestPaid += interestPayment;
        monthlyPrincipalPaid += principalPayment;

        // If this loan is paid off, redistribute its payment to the next loan
        if (loan.remaining_balance <= 0 && i < workingLoans.length - 1) {
          extraPayment = loan.monthly_payment;

          // Re-sort the remaining loans by interest rate for next month
          workingLoans.sort((a: Loan, b: Loan) => b.interest_rate - a.interest_rate);
        }
      }

      // Add to projections
      projections.push({
        month: month + 1,
        date: currentDate,
        remainingBalance,
        interestPaid: monthlyInterestPaid,
        principalPaid: monthlyPrincipalPaid
      });

      // Update totals
      totalInterestPaid += monthlyInterestPaid;

      // Check if all loans are paid off
      allPaidOff = workingLoans.every((loan: any) => loan.remaining_balance <= 0);

      month++;
    }

    return {
      name: "Debt Avalanche",
      description: "Focus on paying off highest interest rate first",
      monthlyPayment: totalMonthlyPayment,
      payoffDate: addMonths(startDate, month - 1),
      totalInterestPaid,
      monthsToPayoff: month,
      projections
    };
  };

  // Chart data preparation
  const getChartData = () => {
    if (!strategies || strategies.length === 0) return null;

    const data = {
      labels: strategies[0].projections.map((p, index) => index % 3 === 0 ? format(p.date, 'MMM yyyy') : ''),
      datasets: strategies.map((strategy, index) => ({
        label: strategy.name,
        data: strategy.projections.map(p => p.remainingBalance),
        borderColor: index === 0 ? 'rgb(99, 102, 241)' : index === 1 ? 'rgb(16, 185, 129)' : 'rgb(245, 158, 11)',
        backgroundColor: index === 0 ? 'rgba(99, 102, 241, 0.1)' : index === 1 ? 'rgba(16, 185, 129, 0.1)' : 'rgba(245, 158, 11, 0.1)',
        borderWidth: 2,
        tension: 0.4,
        fill: false,
        pointRadius: index === activeIndex ? 3 : 1,
        pointHoverRadius: 5
      }))
    };

    return data;
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Loan Balance Projection',
        color: 'rgb(107, 114, 128)'
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`;
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Month',
          color: 'rgb(107, 114, 128)'
        },
        grid: {
          display: false
        }
      },
      y: {
        title: {
          display: true,
          text: 'Remaining Balance',
          color: 'rgb(107, 114, 128)'
        },
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value);
          }
        }
      }
    }
  };

  // Format months as years and months
  const formatMonths = (months: number) => {
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;

    if (years === 0) {
      return `${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;
    } else if (remainingMonths === 0) {
      return `${years} year${years !== 1 ? 's' : ''}`;
    } else {
      return `${years} year${years !== 1 ? 's' : ''} and ${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;
    }
  };

  // Loading state
  if (loansLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  // No loans state
  if (!loans || loans.length === 0) {
    return (
      <div className="p-6 bg-white dark:bg-dark-800 rounded-lg shadow">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Debt Payoff Simulator</h2>
        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-blue-800 dark:text-blue-200">
          <p>You don't have any active loans. Add loans on the Loan Management page to use this simulator.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Debt Payoff Simulator</h1>
      </div>

      <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow space-y-6">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Payment Strategy Comparison</h2>

          {/* Additional Payment Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Additional Monthly Payment
            </label>
            <div className="flex items-center">
              <input
                type="number"
                value={additionalPayment}
                onChange={(e) => setAdditionalPayment(Number(e.target.value))}
                min="0"
                step="1000"
                className="mt-1 block w-full sm:w-64 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white"
              />
              <button
                onClick={() => calculateStrategies()}
                className="ml-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                disabled={isCalculating}
              >
                {isCalculating ? 'Calculating...' : 'Recalculate'}
              </button>
            </div>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Enter an additional amount to add to your monthly payments
            </p>
          </div>

          {/* Strategy Comparisons */}
          {strategies.length > 0 && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {strategies.map((strategy, index) => (
                <div
                  key={strategy.name}
                  className={`border rounded-lg p-4 hover:shadow-md transition-shadow ${
                    index === activeIndex
                      ? 'border-indigo-500 dark:border-indigo-400 bg-indigo-50 dark:bg-indigo-900/20'
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                  onClick={() => setActiveIndex(index)}
                >
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">{strategy.name}</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">{strategy.description}</p>

                  <div className="space-y-3">
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Monthly Payment:</span>
                      <p className="text-lg font-medium text-gray-900 dark:text-white">{formatCurrency(strategy.monthlyPayment)}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Payoff Timeline:</span>
                      <p className="text-lg font-medium text-gray-900 dark:text-white">{formatMonths(strategy.monthsToPayoff)}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Debt-Free Date:</span>
                      <p className="text-lg font-medium text-gray-900 dark:text-white">{format(strategy.payoffDate, 'MMM d, yyyy')}</p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Total Interest:</span>
                      <p className="text-lg font-medium text-gray-900 dark:text-white">{formatCurrency(strategy.totalInterestPaid)}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Chart */}
        {strategies.length > 0 && (
          <div className="mt-8">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Balance Reduction Comparison</h3>
            <div className="h-80">
              <Line data={getChartData() as any} options={chartOptions} />
            </div>
          </div>
        )}

        {/* Interest Savings */}
        {strategies.length > 0 && strategies[0].totalInterestPaid > strategies[1].totalInterestPaid && (
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h3 className="text-lg font-medium text-green-800 dark:text-green-200 mb-2">Potential Interest Savings</h3>
            <p className="text-green-700 dark:text-green-300">
              By using the {strategies[1].totalInterestPaid < strategies[2].totalInterestPaid ? 'Snowball' : 'Avalanche'} strategy,
              you could save <span className="font-bold">{formatCurrency(strategies[0].totalInterestPaid - Math.min(strategies[1].totalInterestPaid, strategies[2].totalInterestPaid))}</span> in
              interest compared to making regular payments.
            </p>
          </div>
        )}
      </div>

      {/* Loan Details */}
      <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Your Loans</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-dark-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Balance</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Monthly Payment</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Interest Rate</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Payoff Date</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-700">
              {loans.map((loan) => (
                <tr key={loan.id} className="hover:bg-gray-50 dark:hover:bg-dark-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{loan.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">{formatCurrency(loan.remaining_balance)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">{formatCurrency(loan.monthly_payment)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">{loan.interest_rate}%</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-gray-900 dark:text-white">
                    {format(addMonths(new Date(loan.next_payment_date), loan.term_months), 'MMM d, yyyy')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
