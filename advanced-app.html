<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Advanced Trading Advisor</title>
  <!-- Include Tailwind CSS from CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Include LightweightCharts -->
  <script src="https://unpkg.com/lightweight-charts@4.1.3/dist/lightweight-charts.standalone.production.js"></script>
  <!-- Include Chart.js for additional charts -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <!-- Include Lucide Icons -->
  <script src="https://unpkg.com/lucide@0.344.0/dist/umd/lucide.min.js"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }
    .card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 1.5rem;
      margin-bottom: 1rem;
    }
    .tab {
      padding: 0.5rem 1rem;
      cursor: pointer;
      border-bottom: 2px solid transparent;
    }
    .tab.active {
      color: #2563eb;
      border-bottom: 2px solid #2563eb;
    }
    .tab-content {
      display: none;
    }
    .tab-content.active {
      display: block;
    }
    .chart-container {
      height: 400px;
      width: 100%;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.5rem 1rem;
      font-weight: 500;
      border-radius: 0.375rem;
      cursor: pointer;
    }
    .btn-primary {
      background-color: #2563eb;
      color: white;
    }
    .btn-primary:hover {
      background-color: #1d4ed8;
    }
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      align-items: center;
      justify-content: center;
      z-index: 50;
    }
    .modal.active {
      display: flex;
    }
    .indicator-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 1rem;
    }
  </style>
</head>
<body>
  <!-- Main App (Initially Visible) -->
  <div id="appContent">
    <!-- Navbar -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex-shrink-0 flex items-center">
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="m22 2-7 20-4-9-9-4Z"/>
                <path d="M22 2 11 13"/>
              </svg>
              <h1 class="ml-2 text-xl font-bold text-gray-900">Trading Advisor</h1>
            </div>
          </div>
          <div class="hidden sm:ml-6 sm:flex sm:items-center">
            <!-- Tabs -->
            <div class="flex space-x-4">
              <button class="tab active px-3 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600" data-tab="dashboard">Dashboard</button>
              <button class="tab px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300" data-tab="technical">Technical Analysis</button>
              <button class="tab px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300" data-tab="risk">Risk Management</button>
              <button class="tab px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300" data-tab="ai">AI Insights</button>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Dashboard Tab -->
      <div id="dashboard-tab" class="tab-content active">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Stock Search & Selector -->
          <div class="card md:col-span-3">
            <div class="flex flex-col md:flex-row md:items-center justify-between mb-4">
              <h2 class="text-xl font-semibold text-gray-800">Stock Selection</h2>
              <div class="flex mt-2 md:mt-0">
                <div class="relative mr-2">
                  <input id="stockSearch" type="text" class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="Search for a stock...">
                  <div id="searchResults" class="absolute z-10 w-full mt-1 bg-white rounded-md shadow-lg hidden"></div>
                </div>
                <button id="loadStockBtn" class="btn btn-primary">Load</button>
              </div>
            </div>
            <div id="stockTickerBtns" class="flex flex-wrap gap-2">
              <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200" data-ticker="AAPL">AAPL</button>
              <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200" data-ticker="MSFT">MSFT</button>
              <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200" data-ticker="GOOGL">GOOGL</button>
              <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200" data-ticker="AMZN">AMZN</button>
              <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200" data-ticker="TSLA">TSLA</button>
            </div>
          </div>
          
          <!-- Stock Chart -->
          <div class="card md:col-span-2">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-xl font-semibold text-gray-800">Stock Chart: <span id="currentStockDisplay">Select a stock</span></h2>
              <div class="flex space-x-2">
                <button class="px-2 py-1 text-sm border border-gray-300 rounded" data-period="1D">1D</button>
                <button class="px-2 py-1 text-sm border border-gray-300 rounded" data-period="1W">1W</button>
                <button class="px-2 py-1 text-sm border border-gray-300 rounded bg-blue-50" data-period="1M">1M</button>
                <button class="px-2 py-1 text-sm border border-gray-300 rounded" data-period="3M">3M</button>
                <button class="px-2 py-1 text-sm border border-gray-300 rounded" data-period="ALL">ALL</button>
              </div>
            </div>
            <div class="chart-container" id="stockChart"></div>
          </div>

          <!-- Technical Indicators -->
          <div class="card">
            <h2 class="text-xl font-semibold mb-4">Technical Indicators</h2>
            <div id="technicalIndicators" class="space-y-4">
              <!-- RSI Indicator -->
              <div class="bg-white rounded-xl p-4 border border-gray-200">
                <div class="flex items-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="22 12 18 12 15 21 9 3 6 12 2 12"/>
                  </svg>
                  <h3 class="text-lg font-semibold text-gray-800">RSI (14)</h3>
                </div>
                <div class="space-y-2">
                  <div id="rsiValue" class="text-2xl font-bold text-gray-900">-</div>
                  <div id="rsiSignal" class="text-lg font-medium">-</div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="rsiBar" class="h-2 rounded-full bg-yellow-500" style="width: 50%"></div>
                  </div>
                </div>
              </div>
              
              <!-- MACD Indicator -->
              <div class="bg-white rounded-xl p-4 border border-gray-200">
                <div class="flex items-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"/>
                    <polyline points="16 7 22 7 22 13"/>
                  </svg>
                  <h3 class="text-lg font-semibold text-gray-800">MACD</h3>
                </div>
                <div class="space-y-2">
                  <div>
                    <div id="macdValue" class="text-2xl font-bold text-gray-900">-</div>
                    <div id="macdSignalValue" class="text-sm text-gray-500">Signal: -</div>
                  </div>
                  <div id="macdSignal" class="text-lg font-medium">-</div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="macdBar" class="h-2 rounded-full bg-green-500" style="width: 0%"></div>
                  </div>
                </div>
              </div>
              
              <!-- Bollinger Bands Indicator -->
              <div class="bg-white rounded-xl p-4 border border-gray-200">
                <div class="flex items-center mb-4">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M5 3c.414 0 .75.336.75.75v14a.75.75 0 0 1-1.5 0v-14c0-.414.336-.75.75-.75"/>
                    <path d="M5 15 Q 10 10, 15 15 T 19 15"/>
                    <path d="M19 3c.414 0 .75.336.75.75v14a.75.75 0 0 1-1.5 0v-14c0-.414.336-.75.75-.75"/>
                  </svg>
                  <h3 class="text-lg font-semibold text-gray-800">Bollinger Bands</h3>
                </div>
                <div class="space-y-2">
                  <div>
                    <div id="bbPrice" class="text-2xl font-bold text-gray-900">-</div>
                    <div class="text-sm text-gray-500">
                      <div id="bbUpper">Upper: -</div>
                      <div id="bbLower">Lower: -</div>
                    </div>
                  </div>
                  <div id="bbSignal" class="text-lg font-medium">-</div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="bbBar" class="h-2 rounded-full bg-green-500" style="width: 50%"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Technical Analysis Tab -->
      <div id="technical-tab" class="tab-content">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Technical Chart -->
          <div class="card">
            <h2 class="text-xl font-semibold mb-4">Technical Chart</h2>
            <div id="technicalChart" class="chart-container"></div>
          </div>
          
          <!-- Indicator Controls -->
          <div class="card">
            <h2 class="text-xl font-semibold mb-4">Indicator Controls</h2>
            <div class="space-y-4">
              <!-- Moving Averages -->
              <div>
                <h3 class="text-lg font-medium mb-2">Moving Averages</h3>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="flex items-center">
                      <input type="checkbox" class="rounded text-blue-600" value="sma20" checked>
                      <span class="ml-2">SMA (20)</span>
                    </label>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <input type="checkbox" class="rounded text-blue-600" value="ema50">
                      <span class="ml-2">EMA (50)</span>
                    </label>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <input type="checkbox" class="rounded text-blue-600" value="sma50">
                      <span class="ml-2">SMA (50)</span>
                    </label>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <input type="checkbox" class="rounded text-blue-600" value="ema200">
                      <span class="ml-2">EMA (200)</span>
                    </label>
                  </div>
                </div>
              </div>
              
              <!-- Technical Indicators -->
              <div>
                <h3 class="text-lg font-medium mb-2">Technical Indicators</h3>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="flex items-center">
                      <input type="checkbox" class="rounded text-blue-600" value="rsi">
                      <span class="ml-2">RSI</span>
                    </label>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <input type="checkbox" class="rounded text-blue-600" value="macd">
                      <span class="ml-2">MACD</span>
                    </label>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <input type="checkbox" class="rounded text-blue-600" value="bb" checked>
                      <span class="ml-2">Bollinger Bands</span>
                    </label>
                  </div>
                  <div>
                    <label class="flex items-center">
                      <input type="checkbox" class="rounded text-blue-600" value="volume" checked>
                      <span class="ml-2">Volume</span>
                    </label>
                  </div>
                </div>
              </div>
              
              <!-- Apply Button -->
              <button id="applyIndicators" class="btn btn-primary w-full">Apply Indicators</button>
            </div>
          </div>
          
          <!-- Detailed Analysis -->
          <div class="card lg:col-span-2">
            <h2 class="text-xl font-semibold mb-4">Detailed Technical Analysis</h2>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Indicator</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Signal</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Analysis</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="technicalAnalysisTable">
                  <!-- Data will be filled dynamically -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Risk Management Tab -->
      <div id="risk-tab" class="tab-content">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Position Sizing Calculator -->
          <div class="card">
            <h2 class="text-xl font-semibold mb-4">Position Sizing Calculator</h2>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Account Size ($)</label>
                <input type="number" id="accountSize" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" value="10000">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Risk Percentage (%)</label>
                <input type="number" id="riskPercentage" step="0.1" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" value="1.0">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Entry Price ($)</label>
                <input type="number" id="entryPrice" step="0.01" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Stop Loss Price ($)</label>
                <input type="number" id="stopLossPrice" step="0.01" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              </div>
              <div>
                <button id="calculatePosition" class="btn btn-primary w-full">Calculate Position Size</button>
              </div>
              <div id="positionResult" class="hidden p-4 bg-blue-50 rounded-md">
                <div class="text-lg font-medium text-blue-800 mb-2">Position Size Result</div>
                <div id="positionSizeShares" class="text-sm">Shares to buy: <span class="font-semibold">-</span></div>
                <div id="positionSizeDollars" class="text-sm">Total position value: <span class="font-semibold">$-</span></div>
                <div id="dollarRisk" class="text-sm">Dollar risk: <span class="font-semibold">$-</span></div>
              </div>
            </div>
          </div>
          
          <!-- Risk/Reward Visualizer -->
          <div class="card">
            <h2 class="text-xl font-semibold mb-4">Risk/Reward Visualizer</h2>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Take Profit Price ($)</label>
                <input type="number" id="takeProfitPrice" step="0.01" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              </div>
              <div id="riskRewardRatio" class="text-lg font-medium">Risk/Reward Ratio: <span>-</span></div>
              <div class="h-8 bg-gray-200 rounded-full overflow-hidden">
                <div class="flex h-8">
                  <div id="riskBar" class="bg-red-500 flex items-center justify-center text-white text-xs" style="width: 50%">Risk</div>
                  <div id="rewardBar" class="bg-green-500 flex items-center justify-center text-white text-xs" style="width: 50%">Reward</div>
                </div>
              </div>
              <div id="tradeAnalysis" class="mt-4 p-4 bg-yellow-50 rounded-md">
                <div class="text-lg font-medium text-yellow-800 mb-2">Trade Analysis</div>
                <div class="text-sm text-yellow-700">
                  Enter your trade parameters to see the analysis.
                </div>
              </div>
            </div>
          </div>
          
          <!-- Trade Journal -->
          <div class="card lg:col-span-2">
            <h2 class="text-xl font-semibold mb-4">Trade Journal</h2>
            <div class="space-y-4">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entry</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exit</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P/L</th>
                      <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200" id="tradeJournalTable">
                    <!-- Will be populated from local storage -->
                  </tbody>
                </table>
              </div>
              <button id="addTradeBtn" class="btn btn-primary">Add New Trade</button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- AI Insights Tab -->
      <div id="ai-tab" class="tab-content">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- AI Prediction Chart -->
          <div class="card lg:col-span-2">
            <h2 class="text-xl font-semibold mb-4">Price Prediction</h2>
            <div id="aiPredictionChart" class="chart-container"></div>
          </div>
          
          <!-- Sentiment Analysis -->
          <div class="card">
            <h2 class="text-xl font-semibold mb-4">Market Sentiment</h2>
            <div class="space-y-4">
              <div>
                <div class="text-xl font-bold mb-2">Overall Sentiment</div>
                <div class="relative pt-1">
                  <div class="flex mb-2 items-center justify-between">
                    <div class="text-xs font-semibold text-red-600 w-1/3 text-left">Bearish</div>
                    <div class="text-xs font-semibold text-yellow-600 w-1/3 text-center">Neutral</div>
                    <div class="text-xs font-semibold text-green-600 w-1/3 text-right">Bullish</div>
                  </div>
                  <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                    <div id="sentimentIndicator" class="transition-all duration-300 shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-green-500" style="width: 75%"></div>
                  </div>
                </div>
              </div>
              
              <!-- Sentiment Sources -->
              <div class="space-y-3">
                <div class="bg-white rounded-lg p-3 border border-gray-200">
                  <div class="font-medium">Technical Analysis</div>
                  <div id="technicalSentiment" class="text-green-600">Bullish</div>
                </div>
                <div class="bg-white rounded-lg p-3 border border-gray-200">
                  <div class="font-medium">On-Chain Data</div>
                  <div id="onChainSentiment" class="text-yellow-600">Neutral</div>
                </div>
                <div class="bg-white rounded-lg p-3 border border-gray-200">
                  <div class="font-medium">News Sentiment</div>
                  <div id="newsSentiment" class="text-green-600">Bullish</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- AI Trading Recommendations -->
          <div class="card lg:col-span-3">
            <h2 class="text-xl font-semibold mb-4">AI Trading Recommendations</h2>
            <div class="p-6 bg-blue-50 rounded-xl">
              <h3 class="text-xl font-bold text-blue-800 mb-4" id="aiRecommendationTitle">Analysis for AAPL</h3>
              <div class="prose max-w-none text-blue-700" id="aiRecommendationContent">
                <p>Please select a stock to see AI-powered trading recommendations.</p>
              </div>
              <div class="mt-6">
                <div class="font-medium text-blue-800 mb-2">Key Metrics:</div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4" id="aiKeyMetrics">
                  <div class="bg-white p-3 rounded-lg shadow-sm">
                    <div class="text-sm text-gray-500">Predicted Direction</div>
                    <div class="font-semibold text-lg" id="predictedDirection">-</div>
                  </div>
                  <div class="bg-white p-3 rounded-lg shadow-sm">
                    <div class="text-sm text-gray-500">Confidence Level</div>
                    <div class="font-semibold text-lg" id="confidenceLevel">-</div>
                  </div>
                  <div class="bg-white p-3 rounded-lg shadow-sm">
                    <div class="text-sm text-gray-500">Recommended Timeframe</div>
                    <div class="font-semibold text-lg" id="recommendedTimeframe">-</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

<script>
  // Global variables
  let stockData = [];
  let currentStock = 'AAPL';
  let currentTimeframe = '1D';
  let mainChart = null;
  let technicalChart = null;
  let aiChart = null;
  
  // Initialize app
  document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');
    
    // Setup tabs
    setupTabs();
    
    // Initialize the app immediately
    initializeApp();
  });
  
  // Set up tab navigation
  function setupTabs() {
    const tabs = document.querySelectorAll('.tab');
    if (tabs.length === 0) {
      console.log('No tab elements found');
      return;
    }
    
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs
        tabs.forEach(t => t.classList.remove('active'));
        tabContents.forEach(c => c.classList.remove('active'));
        
        // Add active class to clicked tab
        tab.classList.add('active');
        const tabId = tab.getAttribute('data-tab');
        if (!tabId) return;
        
        // Correct target ID handling - match the HTML structure
        const tabContentId = tabId + '-tab';
        console.log('Looking for tab content with ID:', tabContentId);
        
        const tabContent = document.getElementById(tabContentId);
        if (tabContent) {
          tabContent.classList.add('active');
          console.log('Tab content activated');
        } else {
          console.log('Tab content not found with ID:', tabContentId);
        }
        
        // Initialize specific tab content if needed
        if (tabId === 'technical' && window.technicalChart) {
          const technicalChartEl = document.getElementById('technicalChart');
          if (window.technicalChart.chart && technicalChartEl) {
            window.technicalChart.chart.applyOptions({ width: technicalChartEl.clientWidth });
          }
        } else if (tabId === 'ai' && window.aiChart) {
          const aiChartEl = document.getElementById('aiChart');
          if (window.aiChart.chart && aiChartEl) {
            window.aiChart.chart.applyOptions({ width: aiChartEl.clientWidth });
          }
        }
      });
    });
  }
  
  // Setup stock ticker buttons
  function setupStockTickerButtons() {
    const stockTickerBtns = document.getElementById('stockTickerBtns');
    
    if (!stockTickerBtns) {
      console.log('Stock ticker buttons container not found');
      return;
    }
    
    // Get all buttons with data-ticker attribute
    const buttons = stockTickerBtns.querySelectorAll('[data-ticker]');
    console.log('Found ticker buttons:', buttons.length);
    
    buttons.forEach(button => {
      button.addEventListener('click', () => {
        const ticker = button.getAttribute('data-ticker');
        console.log('Stock ticker button clicked:', ticker);
        if (ticker) {
          currentStock = ticker;
          loadStockData();
        }
      });
    });
    
    // Also set up the Load button if it exists
    const loadStockBtn = document.getElementById('loadStockBtn');
    if (loadStockBtn) {
      loadStockBtn.addEventListener('click', () => {
        const stockSearch = document.getElementById('stockSearch');
        if (stockSearch && stockSearch.value) {
          currentStock = stockSearch.value.toUpperCase();
          loadStockData();
          stockSearch.value = '';
        }
      });
    }
  }
  
  // Initialize the main stock chart
  function initializeMainChart() {
    try {
      // Clear previous chart if it exists
      const chartContainer = document.getElementById('stockChart');
      if (!chartContainer) {
        console.error('Stock chart container not found');
        return; // Skip if element doesn't exist
      }
      
      chartContainer.innerHTML = '';
      
      // Create chart
      const chart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: 400,
        layout: {
          background: { color: '#ffffff' },
          textColor: '#333',
        },
        grid: {
          vertLines: { color: '#f0f3fa' },
          horzLines: { color: '#f0f3fa' },
        },
        crosshair: {
          mode: LightweightCharts.CrosshairMode.Normal,
        },
        timeScale: {
          borderColor: '#d1d4dc',
          timeVisible: true,
        },
      });
      
      // Create candlestick series
      const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
      });
      
      // Create volume series
      const volumeSeries = chart.addHistogramSeries({
        color: '#26a69a',
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: '',
        scaleMargins: {
          top: 0.8,
          bottom: 0,
        },
      });
      
      // Ensure we have stock data
      if (!stockData || stockData.length === 0) {
        console.error('No stock data available for chart');
        return;
      }
      
      // Set data
      const volumeData = stockData.map(item => ({
        time: item.time,
        value: item.volume,
        color: item.close > item.open ? '#26a69a' : '#ef5350',
      }));
      
      candlestickSeries.setData(stockData);
      volumeSeries.setData(volumeData);
      
      // Handle resize
      window.addEventListener('resize', () => {
        if (chart && chartContainer) {
          chart.applyOptions({ width: chartContainer.clientWidth });
        }
      });
      
      // Set the global chart reference
      window.mainChart = { chart, candlestickSeries, volumeSeries };
      
      console.log('Main chart initialized successfully');
    } catch (error) {
      console.error('Error initializing main chart:', error);
    }
  }
  
  // Mock data generation
  function generateMockStockData(symbol, days) {
    const data = [];
    let basePrice = getBasePrice(symbol);
    let lastClose = basePrice;
    
    const now = new Date();
    
    for (let i = days; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      
      // Skip weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue;
      
      // Generate random price movements
      const volatility = 0.015; // 1.5% daily volatility
      const change = lastClose * volatility * (Math.random() * 2 - 1);
      const open = lastClose;
      const high = Math.max(open, open + change) + (Math.random() * open * 0.005);
      const low = Math.min(open, open + change) - (Math.random() * open * 0.005);
      const close = open + change;
      
      const volume = Math.floor(1000000 + Math.random() * 10000000);
      
      data.push({
        time: Math.floor(date.getTime() / 1000),
        open,
        high,
        low,
        close,
        volume
      });
      
      lastClose = close;
    }
    
    return data;
  }
  
  function getBasePrice(symbol) {
    // Starting prices for different symbols
    const prices = {
      'AAPL': 175.0,
      'MSFT': 330.0,
      'GOOGL': 140.0,
      'AMZN': 130.0,
      'META': 310.0,
      'TSLA': 250.0,
      'NVDA': 430.0,
      'BRK-B': 350.0,
      'JPM': 140.0,
      'V': 240.0
    };
    
    return prices[symbol] || 100.0;
  }
  
  // Update the stock information
  function updateStockInfo() {
    if (stockData.length < 2) return; // Ensure we have enough data points
    
    const lastDataPoint = stockData[stockData.length - 1];
    const prevDataPoint = stockData[stockData.length - 2];
    
    const currentStockDisplay = document.getElementById('currentStockDisplay');
    if (currentStockDisplay) currentStockDisplay.textContent = currentStock;
    
    // Add stock price to the title if it doesn't exist separately
    if (currentStockDisplay) {
      const priceText = ' $' + lastDataPoint.close.toFixed(2);
      if (!currentStockDisplay.textContent.includes('$')) {
        currentStockDisplay.textContent += priceText;
      }
    }
    
    // Update other elements if they exist
    const priceDiff = lastDataPoint.close - prevDataPoint.close;
    const percentDiff = (priceDiff / prevDataPoint.close) * 100;

    updateElementIfExists('stockPrice', '$' + lastDataPoint.close.toFixed(2));
    updateElementIfExists('stockPriceChange', `${priceDiff >= 0 ? '+' : ''}${priceDiff.toFixed(2)} (${percentDiff >= 0 ? '+' : ''}${percentDiff.toFixed(2)}%)`);
    updateElementIfExists('stockOpen', '$' + lastDataPoint.open.toFixed(2));
    updateElementIfExists('stockHigh', '$' + lastDataPoint.high.toFixed(2));
    updateElementIfExists('stockLow', '$' + lastDataPoint.low.toFixed(2));
    updateElementIfExists('stockVolume', lastDataPoint.volume.toLocaleString());
    
    // Update price change styling if element exists
    const priceChangeEl = document.getElementById('stockPriceChange');
    if (priceChangeEl) {
      if (priceDiff >= 0) {
        priceChangeEl.classList.remove('text-red-600');
        priceChangeEl.classList.add('text-green-600');
      } else {
        priceChangeEl.classList.remove('text-green-600');
        priceChangeEl.classList.add('text-red-600');
      }
    }
  }
  
  // Helper function to update element content if it exists
  function updateElementIfExists(id, content) {
    const element = document.getElementById(id);
    if (element) element.textContent = content;
  }
  
  // Calculate technical indicators
  function calculateTechnicalIndicators() {
    if (stockData.length < 15) return; // Ensure we have enough data points
    
    const closes = stockData.map(item => item.close);
    
    // Basic calculations (simplified for this example)
    const lastPrice = closes[closes.length - 1];
    const sma20 = closes.slice(-20).reduce((a, b) => a + b, 0) / Math.min(20, closes.length);
    
    // Example RSI calculation (simplified)
    let gains = 0, losses = 0;
    for (let i = 1; i < Math.min(15, closes.length); i++) {
      const change = closes[closes.length - i] - closes[closes.length - i - 1];
      if (change >= 0) gains += change;
      else losses -= change;
    }
    
    const avgGain = gains / Math.min(14, closes.length - 1);
    const avgLoss = losses / Math.min(14, closes.length - 1);
    const rs = avgLoss === 0 ? 100 : avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));
    
    // Update RSI display if element exists
    updateElementIfExists('rsiValue', rsi.toFixed(2));
    
    // Update other indicator values with null checks
    updateElementIfExists('macdValue', (lastPrice * 0.02).toFixed(2)); // Simplified mock value
    
    updateElementIfExists('bbPrice', lastPrice.toFixed(2));
  }
  
  // Setup stock search functionality
  function setupStockSearch() {
    const searchInput = document.getElementById('stockSearch');
    if (!searchInput) return; // Skip if element doesn't exist
    
    const searchResults = document.getElementById('searchResults');
    
    // Sample stock list
    const stocks = [
      { symbol: 'AAPL', name: 'Apple Inc.' },
      { symbol: 'MSFT', name: 'Microsoft Corporation' },
      { symbol: 'GOOGL', name: 'Alphabet Inc.' },
      { symbol: 'AMZN', name: 'Amazon.com Inc.' },
      { symbol: 'META', name: 'Meta Platforms Inc.' },
      { symbol: 'TSLA', name: 'Tesla Inc.' },
      { symbol: 'NVDA', name: 'NVIDIA Corporation' },
      { symbol: 'BRK-B', name: 'Berkshire Hathaway Inc.' },
      { symbol: 'JPM', name: 'JPMorgan Chase & Co.' },
      { symbol: 'V', name: 'Visa Inc.' }
    ];
    
    // Search handler
    searchInput.addEventListener('input', () => {
      const query = searchInput.value.toLowerCase().trim();
      searchResults.innerHTML = '';
      
      if (query.length < 1) {
        searchResults.classList.add('hidden');
        return;
      }
      
      const matches = stocks.filter(stock => 
        stock.symbol.toLowerCase().includes(query) || 
        stock.name.toLowerCase().includes(query)
      );
      
      if (matches.length > 0) {
        searchResults.classList.remove('hidden');
        matches.forEach(stock => {
          const item = document.createElement('div');
          item.className = 'p-2 hover:bg-gray-100 cursor-pointer';
          item.innerHTML = `<div class="font-medium">${stock.symbol}</div><div class="text-sm text-gray-500">${stock.name}</div>`;
          item.addEventListener('click', () => {
            currentStock = stock.symbol;
            searchInput.value = '';
            searchResults.classList.add('hidden');
            loadStockData();
          });
          searchResults.appendChild(item);
        });
      } else {
        searchResults.classList.add('hidden');
      }
    });
  }
  
  // Load stock data function (simplified)
  function loadStockData() {
    // Generate new mock data for the selected stock
    stockData = generateMockStockData(currentStock, 90);
    
    // Update UI
    updateStockInfo();
    calculateTechnicalIndicators();
    
    // Update chart if it exists
    if (mainChart) {
      mainChart.candlestickSeries.setData(stockData);
      
      const volumeData = stockData.map(item => ({
        time: item.time,
        value: item.volume,
        color: item.close > item.open ? '#26a69a' : '#ef5350',
      }));
      mainChart.volumeSeries.setData(volumeData);
    }
  }
  
  // Basic implementation for updateTimeframe
  function updateTimeframe() {
    // This is a simplified version - just reloads data
    loadStockData();
  }
  
  // Set up timeframe buttons
  function setupTimeframeButtons() {
    // Look for both timeframe-button class and data-period attributes
    const timeframeButtons = document.querySelectorAll('[data-period]');
    console.log('Found timeframe buttons:', timeframeButtons.length);
    
    if (timeframeButtons.length === 0) {
      console.log('No timeframe buttons found');
      return;
    }
    
    timeframeButtons.forEach(button => {
      button.addEventListener('click', () => {
        console.log('Timeframe button clicked:', button.getAttribute('data-period'));
        
        // Remove active class from all buttons
        timeframeButtons.forEach(b => {
          b.classList.remove('active', 'bg-blue-50');
          b.classList.add('border-gray-300');
        });
        
        // Add active class to clicked button
        button.classList.add('active', 'bg-blue-50');
        button.classList.remove('border-gray-300');
        
        const period = button.getAttribute('data-period');
        if (period) {
          currentTimeframe = period;
          updateTimeframe();
        }
      });
    });
    
    // Set default active timeframe button (1M)
    const defaultButton = document.querySelector('[data-period="1M"]');
    if (defaultButton) {
      defaultButton.classList.add('active', 'bg-blue-50');
    }
  }
  
  // Initialize the app after login
  function initializeApp() {
    console.log('Initializing app...');
    
    try {
      // Initialize global variables if not already set
      window.mainChart = window.mainChart || null;
      window.technicalChart = window.technicalChart || null;
      window.aiChart = window.aiChart || null;
      window.currentStock = window.currentStock || 'AAPL';
      window.currentTimeframe = window.currentTimeframe || '1D';
      window.stockData = window.stockData || [];
      
      console.log('Setting up stock ticker buttons...');
      // Set up stock ticker buttons
      setupStockTickerButtons();
      
      console.log('Generating mock stock data for:', currentStock);
      // Generate mock stock data
      stockData = generateMockStockData(currentStock, 90);
      
      console.log('Initializing main chart...');
      // Initialize main chart
      initializeMainChart();
      
      console.log('Updating stock info...');
      // Update stock info and technical indicators
      updateStockInfo();
      
      console.log('Calculating technical indicators...');
      calculateTechnicalIndicators();
      
      console.log('Setting up stock search...');
      // Set up stock search and stock list
      setupStockSearch();
      
      console.log('Setting up timeframe buttons...');
      // Set up timeframe buttons
      setupTimeframeButtons();
      
      console.log('App initialization complete');
    } catch (error) {
      console.error('Error during app initialization:', error);
    }
  }
</script>

</html>
