import { Configuration, PlaidApi, PlaidEnvironments, CountryCode, Products } from 'plaid';

// Simple configuration object without Symbols
const config = {
  basePath: PlaidEnvironments.sandbox,
  baseOptions: {
    headers: {
      'PLAID-CLIENT-ID': import.meta.env.VITE_PLAID_CLIENT_ID || '',
      'PLAID-SECRET': import.meta.env.VITE_PLAID_SECRET || '',
    },
  },
};

// Initialize Plaid client
const plaidClient = new PlaidApi(new Configuration(config));

export async function createLinkToken() {
  if (!import.meta.env.VITE_PLAID_CLIENT_ID || !import.meta.env.VITE_PLAID_SECRET) {
    throw new Error('Plaid configuration is missing. Please check environment variables.');
  }

  try {
    const request = {
      user: { client_user_id: 'user-id' },
      client_name: 'FinanceTrack',
      products: [Products.Transactions],
      country_codes: [CountryCode.Us],
      language: 'en',
    };

    const response = await plaidClient.linkTokenCreate(request);
    return response.data.link_token;
  } catch (error) {
    console.error('Error creating link token:', error);
    throw error;
  }
}

export async function exchangePublicToken(publicToken: string) {
  try {
    const response = await plaidClient.itemPublicTokenExchange({
      public_token: publicToken
    });
    return response.data.access_token;
  } catch (error) {
    console.error('Error exchanging public token:', error);
    throw error;
  }
}

export async function getTransactions(accessToken: string) {
  try {
    const response = await plaidClient.transactionsGet({
      access_token: accessToken,
      start_date: '2024-01-01',
      end_date: '2024-12-31'
    });
    return response.data.transactions;
  } catch (error) {
    console.error('Error fetching transactions:', error);
    throw error;
  }
}