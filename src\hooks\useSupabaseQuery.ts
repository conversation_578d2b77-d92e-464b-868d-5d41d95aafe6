import { useState, useEffect } from 'react';
import { localDataStore } from '../lib/local-data-store';

// Interface for the query result
interface UseSupabaseQueryResult<T> {
  data: T[] | null;
  loading: boolean;
  error: Error | null;
  addItem: (item: Omit<T, 'id' | 'created_at'>) => Promise<T>;
  updateItem: (id: string, updates: Partial<T>) => Promise<T>;
  deleteItem: (id: string) => Promise<void>;
  refreshData: () => Promise<void>;
  refetch: () => Promise<void>;
}

// This is a mock implementation that uses file-based storage instead of Supabase
export function useSupabaseQuery<T>(
  tableName: string,
  options: {
    select?: string;
    orderBy?: { column: string; ascending?: boolean };
    limit?: number;
    filters?: { column: string; value: any }[];
  } = {}
): UseSupabaseQueryResult<T> {
  const [data, setData] = useState<T[] | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Get data from storage using our storage bridge
      let items: T[] = await localDataStore.data.loadCollection<T>(tableName) || [];
      console.log(`Fetched ${items.length} items from ${tableName}`, items);
      
      // Apply filters if provided
      if (options.filters) {
        items = items.filter(item => {
          return options.filters!.every(filter => 
            (item as any)[filter.column] === filter.value
          );
        });
      }

      // Apply ordering if provided
      if (options.orderBy) {
        items.sort((a, b) => {
          const valueA = (a as any)[options.orderBy!.column];
          const valueB = (b as any)[options.orderBy!.column];
          
          if (valueA < valueB) return options.orderBy!.ascending ?? true ? -1 : 1;
          if (valueA > valueB) return options.orderBy!.ascending ?? true ? 1 : -1;
          return 0;
        });
      }

      // Apply limit if provided
      if (options.limit && options.limit > 0) {
        items = items.slice(0, options.limit);
      }

      setData(items);
      setError(null);
    } catch (err) {
      console.error(`Error fetching data from storage for ${tableName}:`, err);
      setError(err instanceof Error ? err : new Error(String(err)));
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [tableName]);

  const addItem = async (item: Omit<T, 'id' | 'created_at'>): Promise<T> => {
    try {
      // Generate a unique ID
      const id = crypto.randomUUID();
      
      // Create the new item with ID and timestamp
      const newItem = {
        ...item,
        id,
        created_at: new Date().toISOString()
      } as unknown as T;
      
      // Save using our data service
      await localDataStore.data.saveData<T>(tableName, newItem);
      console.log(`Added item to ${tableName}:`, newItem);
      
      // Update the state
      setData(prevData => prevData ? [...prevData, newItem] : [newItem]);
      
      return newItem;
    } catch (err) {
      console.error(`Error adding item to storage for ${tableName}:`, err);
      throw err instanceof Error ? err : new Error(String(err));
    }
  };

  const updateItem = async (id: string, updates: Partial<T>): Promise<T> => {
    try {
      // Get the current data
      const currentData = await localDataStore.data.loadCollection<T>(tableName) || [];
      
      // Find the item to update
      const index = currentData.findIndex((item: any) => item.id === id);
      
      if (index === -1) {
        throw new Error(`Item with id ${id} not found in ${tableName}`);
      }
      
      // Create the updated item
      const updatedItem = {
        ...currentData[index],
        ...updates,
      };
      
      // Update the item in the array
      currentData[index] = updatedItem as T;
      
      // Save to storage
      await localDataStore.data.saveCollection(tableName, currentData);
      
      // Update local state after storage operation completes
      setData(prevData => {
        if (!prevData) return [updatedItem as T];
        
        return prevData.map(item => 
          (item as any).id === id ? updatedItem as T : item
        );
      });
      
      console.log(`Updated item in ${tableName}:`, updatedItem);
      return updatedItem as T;
    } catch (error) {
      console.error(`Error updating item in ${tableName}:`, error);
      setError(error instanceof Error ? error : new Error(String(error)));
      throw error;
    }
  };

  const deleteItem = async (id: string): Promise<void> => {
    try {
      // Delete from storage
      await localDataStore.data.deleteData(tableName, id);
      console.log(`Deleted item with id ${id} from ${tableName}`);
      
      // Update the state
      setData(prevData => 
        prevData 
          ? prevData.filter(item => (item as any).id !== id)
          : null
      );
    } catch (err) {
      console.error(`Error deleting item from storage for ${tableName}:`, err);
      throw err instanceof Error ? err : new Error(String(err));
    }
  };

  const refreshData = async (): Promise<void> => {
    await fetchData();
  };

  const refetch = async (): Promise<void> => {
    await refreshData();
  };

  return {
    data,
    loading,
    error,
    addItem,
    updateItem,
    deleteItem,
    refreshData,
    refetch
  };
}
