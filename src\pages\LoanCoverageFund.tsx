import React, { useState, useEffect } from 'react';
import { PlusCircle, Save } from 'lucide-react';
import { format, addMonths, differenceInMonths } from 'date-fns';
import { useAuthStore } from '../store/localAuthStore';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { formatCurrency } from '../utils/currency';
import { Modal } from '../components/Modal';

// Define types needed for this component
interface Loan {
  id: string;
  name: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  remaining_balance: number;
  monthly_payment: number;
  next_payment_date: string;
  created_at: string;
  end_date?: string;
}

interface FundAccount {
  id: string;
  user_id: string;
  balance: number;
  last_updated: string;
}

interface FundAccountForm {
  balance: string;
}

interface LoanSummary {
  loanId: string;
  loanName: string;
  endDate: Date;
  principal: number;
  remainingMonths: number;
  remainingBalance: number;
}

interface MonthlyProjection {
  month: number;
  date: Date;
  payment: number;
  remainingBalance: number;
}

export function LoanCoverageFund() {
  // State for the component
  const [fundAccount, setFundAccount] = useState<FundAccount | null>(null);
  const [formValues, setFormValues] = useState<FundAccountForm>({ balance: '0' });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [coverageMonths, setCoverageMonths] = useState<number>(0);
  const [enhancedCoverageMonths, setEnhancedCoverageMonths] = useState<number>(0);
  const [totalMonthlyDue] = useState<number>(92048); // Default based on your calculation - fixed value
  const [loanSummaries, setLoanSummaries] = useState<LoanSummary[]>([]);
  const [coverageEndDate, setCoverageEndDate] = useState<Date>(new Date());
  const [enhancedCoverageEndDate, setEnhancedCoverageEndDate] = useState<Date>(new Date());
  const [monthlyProjections, setMonthlyProjections] = useState<MonthlyProjection[]>([]);
  const [smartRecommendations, setSmartRecommendations] = useState<any[]>([]);

  const { user } = useAuthStore();

  // Fetch loans
  const {
    data: loans,
    loading: loansLoading,
  } = useSupabaseQuery<Loan>('loans', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Fetch or create fund account
  const {
    data: fundAccounts,
    loading: fundLoading,
    addItem: addFundAccount,
    updateItem: updateFundAccount,
  } = useSupabaseQuery<FundAccount>('fund_accounts', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Initialize or load fund account
  useEffect(() => {
    if (!fundLoading && fundAccounts) {
      if (fundAccounts.length > 0) {
        const account = fundAccounts[0];
        setFundAccount(account);
        setFormValues({ balance: account.balance.toString() });
      } else {
        // Create a fund account if it doesn't exist
        createFundAccount();
      }
    }
  }, [fundAccounts, fundLoading]);

  // Calculate loan summaries and coverage when data changes
  useEffect(() => {
    if (loans && fundAccount) {
      calculateLoanSummaries();
      calculateCoverage();
    }
  }, [loans, fundAccount]);

  // Create a new fund account
  const createFundAccount = async () => {
    try {
      const newAccount = await addFundAccount({
        user_id: user?.id || 'default-user',
        balance: 0,
        last_updated: new Date().toISOString(),
      });

      setFundAccount(newAccount);
      setFormValues({ balance: '0' });
    } catch (error) {
      console.error('Error creating fund account:', error);
    }
  };

  // Update fund account balance
  const updateFundBalance = async () => {
    if (fundAccount) {
      try {
        const updatedAccount = await updateFundAccount(fundAccount.id, {
          balance: parseFloat(formValues.balance),
          last_updated: new Date().toISOString(),
        });

        setFundAccount(updatedAccount);
        setIsModalOpen(false);
        calculateCoverage();
      } catch (error) {
        console.error('Error updating fund account:', error);
      }
    }
  };

  // Calculate loan summaries (remaining months, end dates, etc.)
  const calculateLoanSummaries = () => {
    if (!loans || loans.length === 0) return;

    const summaries: LoanSummary[] = loans.map(loan => {
      // Use the end_date directly from the loan data instead of calculating it
      const startDate = new Date(loan.next_payment_date);
      const endDate = loan.end_date ? new Date(loan.end_date) : addMonths(startDate, loan.term_months);
      const today = new Date();
      const remainingMonths = Math.max(0, differenceInMonths(endDate, today));

      return {
        loanId: loan.id,
        loanName: loan.name,
        endDate,
        principal: loan.principal,
        remainingMonths,
        remainingBalance: loan.remaining_balance
      };
    });

    // Sort by end date (earliest first)
    summaries.sort((a, b) => a.endDate.getTime() - b.endDate.getTime());
    setLoanSummaries(summaries);
  };

  // Calculate coverage based on fund balance and monthly payment
  const calculateCoverage = () => {
    if (!fundAccount) return;

    generateMonthlyProjections();

    // Simple calculation: Fund Balance ÷ Monthly Payment (conservative)
    const exactMonths = fundAccount.balance / totalMonthlyDue;
    const wholeMonths = Math.floor(exactMonths);
    setCoverageMonths(wholeMonths);

    // Corrected calculation: Fund balance already includes CD income
    // Starting June 2025, only deduct loan payments from the fund
    const monthlyFundDepletion = totalMonthlyDue; // EGP 92,048 per month

    // Calculate how many months the fund will last with loan payments
    const enhancedMonths = Math.floor(fundAccount.balance / monthlyFundDepletion);
    setEnhancedCoverageMonths(enhancedMonths);

    // Calculate coverage end dates
    const today = new Date();

    // Simple coverage end date
    const endDate = new Date(today);
    endDate.setMonth(endDate.getMonth() + wholeMonths);
    setCoverageEndDate(endDate);

    // Enhanced coverage end date
    const enhancedEndDate = new Date(today);
    if (enhancedMonths < 999) {
      enhancedEndDate.setMonth(enhancedEndDate.getMonth() + enhancedMonths);
    } else {
      enhancedEndDate.setFullYear(enhancedEndDate.getFullYear() + 10); // Show 10 years in future for "infinite"
    }
    setEnhancedCoverageEndDate(enhancedEndDate);

    // Calculate smart recommendations
    calculateSmartRecommendations();
  };

  // Calculate smart payment recommendations
  const calculateSmartRecommendations = () => {
    if (!loans || !fundAccount) return;

    const recommendations = [];
    const fundBalance = fundAccount.balance; // EGP 662,119
    const monthlyPayment = totalMonthlyDue; // EGP 92,048
    const coverageMonths = enhancedCoverageMonths; // 7 months

    // 1. FUND EXTENSION STRATEGIES
    // Calculate how extra payments can extend fund coverage
    const extraPaymentOptions = [10000, 20000, 30000, 50000];

    extraPaymentOptions.forEach(extraAmount => {
      // Calculate new monthly payment with extra amount
      const newMonthlyPayment = monthlyPayment + extraAmount;
      const newCoverageMonths = Math.floor(fundBalance / newMonthlyPayment);
      const monthsExtended = coverageMonths - newCoverageMonths;

      if (monthsExtended > 0) {
        recommendations.push({
          type: 'fund_extension',
          title: `Extend Fund Coverage`,
          description: `Add ${formatCurrency(extraAmount)}/month to loan payments`,
          impact: `Reduces coverage by ${monthsExtended} months but pays off debt faster`,
          priority: extraAmount <= 30000 ? 'medium' : 'low',
          amount: extraAmount,
          targetLoan: 'Strategic allocation',
          details: {
            currentCoverage: coverageMonths,
            newCoverage: newCoverageMonths,
            monthsReduced: monthsExtended,
            newMonthlyPayment: newMonthlyPayment
          }
        });
      }
    });

    // 2. DEBT PAYOFF ACCELERATION
    // Find highest interest loan for targeted payments
    const sortedByInterest = [...loans].sort((a, b) => b.interest_rate - a.interest_rate);
    const highestInterestLoan = sortedByInterest[0];

    if (highestInterestLoan) {
      // Calculate payoff acceleration for highest interest loan
      const targetExtraPayment = 25000; // EGP 25,000 extra per month
      const currentMonthsToPayoff = Math.ceil(highestInterestLoan.remaining_balance / highestInterestLoan.monthly_payment);
      const newMonthlyPayment = highestInterestLoan.monthly_payment + targetExtraPayment;
      const newMonthsToPayoff = Math.ceil(highestInterestLoan.remaining_balance / newMonthlyPayment);
      const monthsSaved = currentMonthsToPayoff - newMonthsToPayoff;

      // Calculate interest savings
      const totalInterestCurrent = (highestInterestLoan.monthly_payment * currentMonthsToPayoff) - highestInterestLoan.remaining_balance;
      const totalInterestNew = (newMonthlyPayment * newMonthsToPayoff) - highestInterestLoan.remaining_balance;
      const interestSaved = totalInterestCurrent - totalInterestNew;

      if (monthsSaved > 0) {
        recommendations.push({
          type: 'debt_acceleration',
          title: `Accelerate ${highestInterestLoan.name} Payoff`,
          description: `Add ${formatCurrency(targetExtraPayment)}/month to highest interest loan (${highestInterestLoan.interest_rate}%)`,
          impact: `Save ${monthsSaved} months & ${formatCurrency(interestSaved)} in interest`,
          priority: 'high',
          amount: targetExtraPayment,
          targetLoan: highestInterestLoan.name,
          details: {
            currentPayoff: currentMonthsToPayoff,
            newPayoff: newMonthsToPayoff,
            monthsSaved: monthsSaved,
            interestSaved: interestSaved
          }
        });
      }
    }

    // 3. FUND PRESERVATION STRATEGY
    // Suggest ways to extend fund life without extra payments
    recommendations.push({
      type: 'fund_preservation',
      title: 'Preserve Fund Longevity',
      description: 'Maintain current payment schedule to maximize fund duration',
      impact: `Fund lasts ${coverageMonths} months until ${format(addMonths(new Date(), coverageMonths), 'MMM yyyy')}`,
      priority: 'medium',
      amount: 0,
      targetLoan: 'Current strategy',
      details: {
        fundDuration: coverageMonths,
        depletionDate: format(addMonths(new Date(), coverageMonths), 'MMM yyyy'),
        monthlyDepletion: monthlyPayment
      }
    });

    // 4. POST-FUND PLANNING
    // Suggest preparation for when fund runs out
    const postFundDate = addMonths(new Date(), coverageMonths);
    recommendations.push({
      type: 'post_fund_planning',
      title: 'Plan Post-Fund Strategy',
      description: `Prepare alternative funding for ${format(postFundDate, 'MMM yyyy')} onwards`,
      impact: 'Ensure seamless loan payment continuation',
      priority: 'high',
      amount: monthlyPayment,
      targetLoan: 'Future planning',
      details: {
        planningDate: format(postFundDate, 'MMM yyyy'),
        monthlyNeed: monthlyPayment,
        remainingLoans: loans.length
      }
    });

    // 5. LOAN PRIORITIZATION
    // Suggest which loans to focus on based on interest rates and balances
    if (loans.length > 1) {
      const totalDebt = loans.reduce((sum, loan) => sum + loan.remaining_balance, 0);
      const weightedAvgRate = loans.reduce((sum, loan) => sum + (loan.remaining_balance * loan.interest_rate), 0) / totalDebt;

      recommendations.push({
        type: 'loan_prioritization',
        title: 'Optimize Loan Payment Order',
        description: `Focus on highest interest loans first (avg rate: ${weightedAvgRate.toFixed(1)}%)`,
        impact: 'Minimize total interest paid over loan lifetime',
        priority: 'medium',
        amount: 0,
        targetLoan: `${sortedByInterest.slice(0, 2).map(l => l.name).join(', ')}`,
        details: {
          averageRate: weightedAvgRate,
          totalDebt: totalDebt,
          highestRateLoan: highestInterestLoan.name,
          highestRate: highestInterestLoan.interest_rate
        }
      });
    }

    setSmartRecommendations(recommendations);
  };

  // Generate the monthly projection table
  const generateMonthlyProjections = () => {
    if (!fundAccount) return;

    // Corrected projection: Fund starts at EGP 662,119 (already includes CD income)
    // Each month: Fund balance - loan payment (EGP 92,048) = fund decreases
    const projectionData = [];
    let currentBalance = fundAccount.balance; // EGP 662,119

    // May 2025 - No payments yet, fund stays the same
    projectionData.push({
      month: 1,
      date: new Date(2025, 4, 1), // May 2025
      payment: 0,
      balance: currentBalance
    });

    // June 2025 onwards - Start loan payments (fund decreases)
    for (let month = 2; month <= 12; month++) {
      const monthDate = new Date(2025, 4 + month - 1, 1); // May + (month-1)

      // Calculate payment amount (starts June 2025)
      let paymentAmount = totalMonthlyDue; // EGP 92,048

      // After some loans are paid off (estimated month 8), reduce payments
      if (month > 8) {
        paymentAmount = Math.max(0, totalMonthlyDue * 0.22); // Assume significant reduction
      }

      // Deduct payment from fund balance
      currentBalance -= paymentAmount;

      projectionData.push({
        month,
        date: monthDate,
        payment: paymentAmount,
        balance: currentBalance
      });
    }

    const manualProjection = projectionData;

    // Convert manual projection to the right format
    setMonthlyProjections(manualProjection.map(item => {
      return {
        month: item.month,
        date: item.date,
        payment: item.payment,
        remainingBalance: item.balance
      };
    }));
  };

  // Helper function to handle form changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormValues({
      ...formValues,
      [name]: value,
    });
  };

  // Loading state
  if (loansLoading || fundLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Loan Coverage Fund</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => setIsModalOpen(true)}
            className="flex items-center px-4 py-2 bg-indigo-600 dark:bg-indigo-500 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600"
          >
            <PlusCircle className="w-5 h-5 mr-2" />
            Update Fund Balance
          </button>
        </div>
      </div>

      {/* Fund Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Fund Balance</h3>
          <p className="mt-2 text-3xl font-bold text-indigo-600 dark:text-indigo-400">
            {formatCurrency(fundAccount?.balance || 0)}
          </p>
          <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">
            Last updated: {fundAccount ? format(new Date(fundAccount.last_updated), 'MMM d, yyyy') : '-'}
          </p>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Monthly Loan Payments</h3>
          <p className="mt-2 text-3xl font-bold text-rose-600 dark:text-rose-400">
            {formatCurrency(totalMonthlyDue)}
          </p>
          <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">Total loan obligations</p>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border-l-4 border-orange-400">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Conservative Coverage</h3>
          <p className="mt-2 text-3xl font-bold text-orange-600 dark:text-orange-400">{coverageMonths} months</p>
          <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">
            Until {format(coverageEndDate, 'MMM d, yyyy')}
          </p>
          <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
            <p>Fund only, no income</p>
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border-l-4 border-emerald-400">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Realistic Coverage</h3>
          <p className="mt-2 text-3xl font-bold text-emerald-600 dark:text-emerald-400">
            {enhancedCoverageMonths >= 999 ? '∞' : `${enhancedCoverageMonths} months`}
          </p>
          <p className="mt-1 text-sm text-gray-700 dark:text-gray-300">
            {enhancedCoverageMonths >= 999 ? 'Fund grows monthly' : `Until ${format(enhancedCoverageEndDate, 'MMM d, yyyy')}`}
          </p>
          <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
            <p>Including CD income</p>
          </div>
        </div>
      </div>

      {/* Enhanced Analysis Section */}
      <div className="bg-gradient-to-r from-emerald-50 to-blue-50 dark:from-emerald-900/20 dark:to-blue-900/20 p-6 rounded-lg shadow-sm border border-emerald-200 dark:border-emerald-800">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">💡 Enhanced Financial Analysis</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Fund Status</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Current Balance:</span>
                <span className="font-medium text-blue-600 dark:text-blue-400">{formatCurrency(fundAccount?.balance || 0)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Coverage Duration:</span>
                <span className="font-medium text-emerald-600 dark:text-emerald-400">{enhancedCoverageMonths} months</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Depletion Date:</span>
                <span className="font-medium text-orange-600 dark:text-orange-400">
                  {format(addMonths(new Date(), enhancedCoverageMonths), 'MMM yyyy')}
                </span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Monthly Breakdown</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Loan Payments:</span>
                <span className="font-medium text-rose-600 dark:text-rose-400">{formatCurrency(totalMonthlyDue)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Fund Usage Rate:</span>
                <span className="font-medium text-orange-600 dark:text-orange-400">
                  {fundAccount?.balance ? ((totalMonthlyDue / fundAccount.balance) * 100).toFixed(1) : '0.0'}%/month
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Remaining After Payment:</span>
                <span className="font-medium text-blue-600 dark:text-blue-400">{formatCurrency((fundAccount?.balance || 0) - totalMonthlyDue)}</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Strategic Insights</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center text-blue-700 dark:text-blue-300">
                <span className="mr-2">📊</span>
                <span>Fund covers {enhancedCoverageMonths} months of payments</span>
              </div>
              <div className="flex items-center text-orange-700 dark:text-orange-300">
                <span className="mr-2">⏰</span>
                <span>Plan funding post-{format(addMonths(new Date(), enhancedCoverageMonths), 'MMM yyyy')}</span>
              </div>
              <div className="flex items-center text-emerald-700 dark:text-emerald-300">
                <span className="mr-2">💡</span>
                <span>Extra payments extend coverage</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Smart Payment Recommendations */}
      {smartRecommendations.length > 0 && (
        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">🎯 Smart Payment Recommendations</h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {smartRecommendations.map((rec, index) => (
              <div
                key={index}
                className={`p-5 rounded-lg border-l-4 ${
                  rec.priority === 'high'
                    ? 'border-red-400 bg-red-50 dark:bg-red-900/20'
                    : rec.priority === 'medium'
                    ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/20'
                    : 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className={`font-semibold text-base ${
                        rec.priority === 'high'
                          ? 'text-red-800 dark:text-red-300'
                          : rec.priority === 'medium'
                          ? 'text-yellow-800 dark:text-yellow-300'
                          : 'text-blue-800 dark:text-blue-300'
                      }`}>
                        {rec.title}
                      </h4>
                      <div className={`px-2 py-1 rounded text-xs font-medium ${
                        rec.priority === 'high'
                          ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                          : rec.priority === 'medium'
                          ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100'
                      }`}>
                        {rec.priority.toUpperCase()}
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {rec.description}
                    </p>

                    <div className={`p-3 rounded-md mb-3 ${
                      rec.priority === 'high'
                        ? 'bg-red-100 dark:bg-red-900/30'
                        : rec.priority === 'medium'
                        ? 'bg-yellow-100 dark:bg-yellow-900/30'
                        : 'bg-blue-100 dark:bg-blue-900/30'
                    }`}>
                      <p className={`text-sm font-medium ${
                        rec.priority === 'high'
                          ? 'text-red-700 dark:text-red-300'
                          : rec.priority === 'medium'
                          ? 'text-yellow-700 dark:text-yellow-300'
                          : 'text-blue-700 dark:text-blue-300'
                      }`}>
                        💡 {rec.impact}
                      </p>
                    </div>

                    {/* Additional Details */}
                    <div className="space-y-2 text-xs text-gray-500 dark:text-gray-400">
                      {rec.amount > 0 && (
                        <div className="flex justify-between">
                          <span>Monthly Amount:</span>
                          <span className="font-medium">{formatCurrency(rec.amount)}</span>
                        </div>
                      )}
                      {rec.targetLoan !== 'N/A' && rec.targetLoan !== 'Current strategy' && rec.targetLoan !== 'Future planning' && (
                        <div className="flex justify-between">
                          <span>Target:</span>
                          <span className="font-medium">{rec.targetLoan}</span>
                        </div>
                      )}
                      {rec.details && (
                        <>
                          {rec.details.monthsSaved && (
                            <div className="flex justify-between">
                              <span>Time Saved:</span>
                              <span className="font-medium text-emerald-600">{rec.details.monthsSaved} months</span>
                            </div>
                          )}
                          {rec.details.interestSaved && (
                            <div className="flex justify-between">
                              <span>Interest Saved:</span>
                              <span className="font-medium text-emerald-600">{formatCurrency(rec.details.interestSaved)}</span>
                            </div>
                          )}
                          {rec.details.newCoverage && (
                            <div className="flex justify-between">
                              <span>New Coverage:</span>
                              <span className="font-medium">{rec.details.newCoverage} months</span>
                            </div>
                          )}
                          {rec.details.depletionDate && (
                            <div className="flex justify-between">
                              <span>Fund Depletion:</span>
                              <span className="font-medium">{rec.details.depletionDate}</span>
                            </div>
                          )}
                          {rec.details.planningDate && (
                            <div className="flex justify-between">
                              <span>Planning Date:</span>
                              <span className="font-medium text-orange-600">{rec.details.planningDate}</span>
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-emerald-50 dark:from-blue-900/20 dark:to-emerald-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">📊 Strategic Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600 dark:text-gray-400">Current Fund Coverage:</span>
                <p className="font-semibold text-blue-600 dark:text-blue-400">{enhancedCoverageMonths} months</p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Monthly Depletion:</span>
                <p className="font-semibold text-rose-600 dark:text-rose-400">{formatCurrency(totalMonthlyDue)}</p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">Planning Horizon:</span>
                <p className="font-semibold text-orange-600 dark:text-orange-400">{format(addMonths(new Date(), enhancedCoverageMonths), 'MMM yyyy')}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loan Payment Schedule Table */}
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Loan Payment Schedule</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead className="bg-gray-100 dark:bg-dark-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Loan
                </th>
                <th className="px-6 py-3 text-right text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Principal
                </th>
                <th className="px-6 py-3 text-right text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Remaining Balance
                </th>
                <th className="px-6 py-3 text-right text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  End Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Remaining Months
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-600">
              {loanSummaries.map((loan) => (
                <tr key={loan.loanId} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-gray-200">
                    {loan.loanName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-800 dark:text-gray-200">
                    {formatCurrency(loan.principal)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-800 dark:text-gray-200">
                    {formatCurrency(loan.remainingBalance)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-800 dark:text-gray-200">
                    {format(loan.endDate, 'MMM d, yyyy')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-800 dark:text-gray-200">
                    {loan.remainingMonths}
                  </td>
                </tr>
              ))}
              {loanSummaries.length === 0 && (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-gray-700 dark:text-gray-300">
                    No loans found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Monthly Projection Table */}
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm mt-6">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Monthly Fund Projection</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
            <thead className="bg-gray-100 dark:bg-dark-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Month
                </th>
                <th className="px-6 py-3 text-left text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Payment Amount
                </th>
                <th className="px-6 py-3 text-right text-xs font-semibold text-gray-800 dark:text-gray-200 uppercase tracking-wider">
                  Opening Balance
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-600">
              {monthlyProjections.map((projection) => (
                <tr
                  key={projection.month}
                  className={`hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    projection.remainingBalance < 0 ? 'bg-red-50 dark:bg-red-900/20' : ''
                  }`}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-gray-200">
                    {projection.month}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-gray-200">
                    {format(projection.date, 'd-MMM-yy')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-right font-medium text-gray-800 dark:text-gray-200">
                    {formatCurrency(projection.payment)}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${
                    projection.remainingBalance < 0
                      ? 'text-red-600 dark:text-red-400'
                      : 'text-gray-800 dark:text-gray-200'
                  }`}>
                    {formatCurrency(projection.remainingBalance)}
                  </td>
                </tr>
              ))}
              {monthlyProjections.length === 0 && (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-gray-700 dark:text-gray-300">
                    Update your fund balance to see monthly projections.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Fund Calculation Details */}
      <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Fund Calculation Details</h3>

        <div className="space-y-3">
          <div className="flex justify-between items-center pb-2 border-b border-gray-200 dark:border-gray-600">
            <span className="text-gray-700 dark:text-gray-300">Total Monthly Loan Payments</span>
            <span className="font-semibold text-gray-900 dark:text-white">EGP 198,048</span>
          </div>

          <div className="flex justify-between items-center pb-2 border-b border-gray-200 dark:border-gray-600">
            <span className="text-gray-700 dark:text-gray-300">Total CD Income (excluding CIB)</span>
            <span className="font-semibold text-gray-900 dark:text-white">EGP 118,333</span>
          </div>

          <div className="flex justify-between items-center pb-2 border-b border-gray-200 dark:border-gray-600">
            <span className="text-gray-700 dark:text-gray-300">Credit Card Payment</span>
            <span className="font-semibold text-red-600 dark:text-red-400">- EGP 7,000</span>
          </div>

          <div className="flex justify-between items-center pb-2 border-b border-gray-200 dark:border-gray-600">
            <span className="text-gray-700 dark:text-gray-300">Insurance Payment</span>
            <span className="font-semibold text-red-600 dark:text-red-400">- EGP 5,000</span>
          </div>

          <div className="flex justify-between items-center pt-2">
            <span className="font-medium text-gray-800 dark:text-gray-200">Net Monthly Payment Needed</span>
            <span className="font-bold text-rose-600 dark:text-rose-400">EGP 92,048</span>
          </div>
        </div>
      </div>

      {/* Update Fund Balance Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Update Fund Balance"
      >
        <form onSubmit={(e) => { e.preventDefault(); updateFundBalance(); }} className="space-y-4">
          <div>
            <label htmlFor="balance" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Current Fund Balance
            </label>
            <input
              type="number"
              id="balance"
              name="balance"
              value={formValues.balance}
              onChange={handleInputChange}
              min="0"
              step="0.01"
              className="mt-1 block w-full rounded-md border-gray-300 dark:border-dark-500 shadow-sm focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500 dark:focus:ring-indigo-400"
              required
            />
          </div>

          <div className="flex justify-end space-x-2">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="px-4 py-2 border rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-indigo-600 dark:bg-indigo-500 text-white rounded-md hover:bg-indigo-700 dark:hover:bg-indigo-600"
            >
              <Save className="w-4 h-4 mr-2 inline" />
              Save Balance
            </button>
          </div>
        </form>
      </Modal>
    </div>
  );
}
