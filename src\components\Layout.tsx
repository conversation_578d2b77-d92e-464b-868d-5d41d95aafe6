import { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  PiggyBank,
  Receipt,
  Wallet,
  Building2,
  Banknote,
  BarChart2,
  Target,
  Calendar,
  DollarSign,
  Settings as SettingsIcon,
  LogOut,
  Menu,
  X,
  LineChart,
  TrendingUp
} from 'lucide-react';
import { useAuthStore } from '../store/localAuthStore';

export function Layout() {
  const location = useLocation();
  const signOut = useAuthStore((state) => state.signOut);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navigation = [
    { name: 'Dashboard', href: '/', icon: LayoutDashboard },
    { name: 'Bank Accounts', href: '/bank-accounts', icon: Building2 },
    { name: 'Budgets', href: '/budgets', icon: PiggyBank },
    { name: 'Expenses', href: '/expenses', icon: Receipt },
    { name: 'Loans', href: '/loans', icon: Wallet },
    { name: 'Loan Coverage Fund', href: '/loan-coverage-fund', icon: DollarSign },
    { name: 'CDs', href: '/cds', icon: Banknote },
    { name: 'Goals', href: '/goals', icon: Target },
    { name: 'Calendar', href: '/calendar', icon: Calendar },
    { name: 'Financial Calendar', href: '/financial-calendar', icon: Calendar },
    { name: 'Net Worth', href: '/net-worth', icon: DollarSign },
    { name: 'Reports', href: '/reports', icon: BarChart2 },
    { name: 'Debt Payoff Simulator', href: '/debt-payoff-simulator', icon: LineChart },
    { name: 'Financial Timeline', href: '/financial-timeline', icon: Calendar },
    { name: 'Timeline Projections', href: '/timeline-projections', icon: TrendingUp },
    { name: 'Net Worth Projection', href: '/net-worth-projection', icon: TrendingUp },
    { name: 'Settings', href: '/settings', icon: SettingsIcon }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-900">
      <div className="lg:hidden">
        <div className="flex items-center justify-between px-4 py-3 bg-white dark:bg-dark-800 shadow-sm">
          <h1 className="text-xl font-bold text-indigo-600 dark:text-indigo-400">FinanceTrack</h1>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white"
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {isMobileMenuOpen && (
          <div className="fixed inset-0 z-40 bg-black bg-opacity-25" onClick={() => setIsMobileMenuOpen(false)}>
            <div className="fixed inset-y-0 left-0 w-64 bg-white dark:bg-dark-800 shadow-lg" onClick={e => e.stopPropagation()}>
              <div className="h-full flex flex-col">
                <div className="flex-1 overflow-y-auto">
                  <nav className="px-2 py-4">
                    {navigation.map((item) => {
                      const Icon = item.icon;
                      return (
                        <Link
                          key={item.name}
                          to={item.href}
                          onClick={() => setIsMobileMenuOpen(false)}
                          className={`
                            flex items-center px-4 py-3 text-sm rounded-md mb-1
                            ${location.pathname === item.href
                              ? 'bg-indigo-50 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400'
                              : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400'
                            }
                          `}
                        >
                          <Icon className="h-5 w-5 mr-3" />
                          {item.name}
                        </Link>
                      );
                    })}
                  </nav>
                </div>
                <div className="p-4 border-t border-gray-200 dark:border-dark-700">
                  <button
                    onClick={() => {
                      signOut();
                      setIsMobileMenuOpen(false);
                    }}
                    className="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400 rounded-md"
                  >
                    <LogOut className="h-5 w-5 mr-3" />
                    Sign Out
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="flex h-screen">
        {/* Desktop Sidebar */}
        <div className="hidden lg:block w-64 bg-white dark:bg-dark-800 shadow-lg">
          <div className="flex h-16 items-center justify-center">
            <h1 className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">FinanceTrack</h1>
          </div>
          <nav className="mt-6">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`
                    flex items-center px-6 py-3 text-sm
                    ${location.pathname === item.href
                      ? 'bg-indigo-50 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400'
                    }
                  `}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>
          <div className="absolute bottom-0 w-64 p-4">
            <button
              onClick={() => signOut()}
              className="flex items-center px-6 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700 hover:text-indigo-600 dark:hover:text-indigo-400 w-full"
            >
              <LogOut className="h-5 w-5 mr-3" />
              Sign Out
            </button>
          </div>
        </div>

        {/* Main content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <main className="flex-1 overflow-y-auto p-4 lg:p-8">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
}