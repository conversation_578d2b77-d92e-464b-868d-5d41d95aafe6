
import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { format, subMonths } from 'date-fns';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

export function Reports() {
  // Sample data for the monthly spending trend
  const monthlySpendingData = {
    labels: Array.from({ length: 6 }, (_, i) => {
      const date = subMonths(new Date(), 5 - i);
      return format(date, 'MMM yyyy');
    }),
    datasets: [
      {
        label: 'Monthly Spending',
        data: [3200, 3400, 3100, 3600, 3300, 3240],
        borderColor: 'rgb(99, 102, 241)',
        backgroundColor: 'rgba(99, 102, 241, 0.5)',
        tension: 0.4,
      },
    ],
  };

  // Sample data for expense categories
  const categoryData = {
    labels: ['Housing', 'Transportation', 'Food', 'Entertainment', 'Utilities', 'Other'],
    datasets: [
      {
        data: [1800, 450, 600, 250, 380, 200],
        backgroundColor: [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 206, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)',
          'rgba(255, 159, 64, 0.8)',
        ],
      },
    ],
  };

  const lineChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Monthly Spending Trend',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: (value: string | number) => `$${value}`,
        },
      },
    },
  };

  const doughnutChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: true,
        text: 'Expenses by Category',
      },
    },
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Financial Reports</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <Line options={lineChartOptions} data={monthlySpendingData} />
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm">
          <Doughnut options={doughnutChartOptions} data={categoryData} />
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Monthly Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Total Income</h3>
            <p className="mt-1 text-2xl font-semibold text-gray-900">$5,000</p>
            <p className="text-sm text-green-600">+12% from last month</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Total Expenses</h3>
            <p className="mt-1 text-2xl font-semibold text-gray-900">$3,240</p>
            <p className="text-sm text-rose-600">+5% from last month</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-500">Savings Rate</h3>
            <p className="mt-1 text-2xl font-semibold text-gray-900">35.2%</p>
            <p className="text-sm text-gray-500">Target: 30%</p>
          </div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Financial Goals Progress</h2>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">Emergency Fund</span>
              <span className="text-sm font-medium text-gray-700">75%</span>
            </div>
            <div className="h-2 bg-gray-200 rounded-full">
              <div className="h-full bg-green-500 rounded-full" style={{ width: '75%' }} />
            </div>
          </div>
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">Retirement Savings</span>
              <span className="text-sm font-medium text-gray-700">45%</span>
            </div>
            <div className="h-2 bg-gray-200 rounded-full">
              <div className="h-full bg-blue-500 rounded-full" style={{ width: '45%' }} />
            </div>
          </div>
          <div>
            <div className="flex justify-between mb-1">
              <span className="text-sm font-medium text-gray-700">Debt Reduction</span>
              <span className="text-sm font-medium text-gray-700">60%</span>
            </div>
            <div className="h-2 bg-gray-200 rounded-full">
              <div className="h-full bg-indigo-500 rounded-full" style={{ width: '60%' }} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}