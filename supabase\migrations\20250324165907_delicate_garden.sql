/*
  # Fix stock search functionality

  1. Changes
    - Update RLS policies to allow all authenticated users to read stocks
    - Add default user_id for initial stocks
    - Ensure stocks are publicly readable

  2. Security
    - Maintain RLS but allow public read access to stocks
    - Keep write operations restricted to owners
*/

-- First, update the RLS policy for stocks to allow all authenticated users to read
DROP POLICY IF EXISTS "Users can read own stocks" ON stocks;
CREATE POLICY "Anyone can read stocks"
  ON stocks
  FOR SELECT
  TO authenticated
  USING (true);

-- Update existing stocks to have a default admin user_id
DO $$
DECLARE
  admin_id uuid;
BEGIN
  -- Get or create admin user
  SELECT id INTO admin_id
  FROM auth.users
  WHERE email = '<EMAIL>'
  LIMIT 1;

  -- Update existing stocks to have the admin user_id
  UPDATE stocks
  SET user_id = admin_id
  WHERE user_id IS NULL;

  -- Make sure all required columns are set
  UPDATE stocks
  SET 
    created_at = COALESCE(created_at, now()),
    updated_at = COALESCE(updated_at, now());
END $$;