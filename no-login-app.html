<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Advanced Trading Advisor</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
</head>
<body class="bg-gray-100">
  <div id="appContent">
    <!-- Navbar -->
    <nav class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex-shrink-0 flex items-center">
            <h1 class="text-xl font-bold text-blue-600">Advanced Trading Advisor</h1>
          </div>
          <div class="ml-6 flex items-center">
            <span class="hidden md:block text-sm text-gray-500">Welcome, User</span>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Dashboard -->
      <div class="tab-content active">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- Stock Selection -->
          <div class="bg-white p-6 rounded-lg shadow md:col-span-3">
            <div class="flex flex-col md:flex-row md:items-center justify-between mb-4">
              <h2 class="text-xl font-semibold text-gray-800">Stock Selection</h2>
              
              <div class="flex items-center mt-4 md:mt-0">
                <div class="relative">
                  <input id="stockSearch" type="text" placeholder="Search stocks..." class="border rounded-lg py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <button id="loadStockBtn" class="bg-blue-500 text-white px-4 py-2 rounded-lg ml-2">Load</button>
                </div>
              </div>
            </div>
            
            <div id="stockTickerBtns" class="flex flex-wrap gap-2">
              <button data-ticker="AAPL" class="bg-blue-100 hover:bg-blue-200 text-blue-800 font-medium px-4 py-2 rounded-lg">AAPL</button>
              <button data-ticker="MSFT" class="bg-gray-100 hover:bg-blue-200 text-gray-800 font-medium px-4 py-2 rounded-lg">MSFT</button>
              <button data-ticker="GOOGL" class="bg-gray-100 hover:bg-blue-200 text-gray-800 font-medium px-4 py-2 rounded-lg">GOOGL</button>
              <button data-ticker="AMZN" class="bg-gray-100 hover:bg-blue-200 text-gray-800 font-medium px-4 py-2 rounded-lg">AMZN</button>
              <button data-ticker="META" class="bg-gray-100 hover:bg-blue-200 text-gray-800 font-medium px-4 py-2 rounded-lg">META</button>
            </div>
          </div>
          
          <!-- Stock Chart -->
          <div class="bg-white p-6 rounded-lg shadow md:col-span-2">
            <div class="flex items-center justify-between mb-4">
              <h2 id="currentStockDisplay" class="text-xl font-semibold text-gray-800">AAPL</h2>
              <div class="flex space-x-2">
                <button data-period="1D" class="border border-gray-300 px-3 py-1 rounded-md text-sm">1D</button>
                <button data-period="1W" class="border border-gray-300 px-3 py-1 rounded-md text-sm">1W</button>
                <button data-period="1M" class="border border-gray-300 px-3 py-1 rounded-md text-sm">1M</button>
                <button data-period="3M" class="border border-gray-300 px-3 py-1 rounded-md text-sm">3M</button>
                <button data-period="1Y" class="border border-gray-300 px-3 py-1 rounded-md text-sm">1Y</button>
              </div>
            </div>
            <div id="stockChart" class="w-full h-80 md:h-96"></div>
          </div>
          
          <!-- Stock Info -->
          <div class="bg-white p-6 rounded-lg shadow">
            <h2 class="text-xl font-semibold mb-4">Stock Information</h2>
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <p class="text-sm text-gray-500">Price</p>
                  <p id="stockPrice" class="text-lg font-semibold">$0.00</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Change</p>
                  <p id="stockPriceChange" class="text-lg font-semibold text-green-600">+0.00 (0.00%)</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Open</p>
                  <p id="stockOpen" class="text-lg font-semibold">$0.00</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Volume</p>
                  <p id="stockVolume" class="text-lg font-semibold">0</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">High</p>
                  <p id="stockHigh" class="text-lg font-semibold">$0.00</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Low</p>
                  <p id="stockLow" class="text-lg font-semibold">$0.00</p>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Technical Indicators -->
          <div class="bg-white p-6 rounded-lg shadow md:col-span-3">
            <h2 class="text-xl font-semibold mb-4">Technical Indicators</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <!-- RSI Indicator -->
              <div class="bg-white rounded-xl p-4 border border-gray-200">
                <div class="flex items-center justify-between">
                  <h3 class="font-medium text-gray-800">RSI</h3>
                  <span id="rsiValue" class="text-lg font-bold">50.00</span>
                </div>
                <div class="mt-2 h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div class="bg-gradient-to-r from-green-500 via-yellow-500 to-red-500 h-full" style="width: 50%"></div>
                </div>
                <div class="flex justify-between mt-1 text-xs text-gray-500">
                  <span>Oversold</span>
                  <span>Neutral</span>
                  <span>Overbought</span>
                </div>
              </div>
              
              <!-- MACD Indicator -->
              <div class="bg-white rounded-xl p-4 border border-gray-200">
                <div class="flex items-center justify-between">
                  <h3 class="font-medium text-gray-800">MACD</h3>
                  <span id="macdValue" class="text-lg font-bold">0.00</span>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                  <p>Signal: <span class="font-medium text-blue-600">Neutral</span></p>
                </div>
              </div>
              
              <!-- Bollinger Bands -->
              <div class="bg-white rounded-xl p-4 border border-gray-200">
                <div class="flex items-center justify-between">
                  <h3 class="font-medium text-gray-800">Bollinger Bands</h3>
                  <span id="bbPrice" class="text-lg font-bold">$0.00</span>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                  <p>Signal: <span class="font-medium text-blue-600">Within Bands</span></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Global variables
    let stockData = [];
    let currentStock = 'AAPL';
    let currentTimeframe = '1M';
    let mainChart = null;
    
    // Initialize app
    document.addEventListener('DOMContentLoaded', function() {
      console.log('DOM Content Loaded');
      
      // Set up event listeners for ticker buttons
      setupStockTickerButtons();
      
      // Generate initial stock data
      stockData = generateMockStockData(currentStock, 90);
      
      // Initialize chart
      initializeMainChart();
      
      // Update stock info
      updateStockInfo();
      
      // Set up timeframe buttons
      setupTimeframeButtons();
    });
    
    // Set up timeframe buttons
    function setupTimeframeButtons() {
      const timeframeButtons = document.querySelectorAll('[data-period]');
      console.log('Found timeframe buttons:', timeframeButtons.length);
      
      timeframeButtons.forEach(button => {
        button.addEventListener('click', () => {
          console.log('Timeframe button clicked:', button.getAttribute('data-period'));
          
          // Remove active class from all buttons
          timeframeButtons.forEach(b => {
            b.classList.remove('bg-blue-50');
            b.classList.add('border-gray-300');
          });
          
          // Add active class to clicked button
          button.classList.add('bg-blue-50');
          button.classList.remove('border-gray-300');
          
          const period = button.getAttribute('data-period');
          if (period) {
            currentTimeframe = period;
            updateTimeframe();
          }
        });
      });
      
      // Set default active timeframe button (1M)
      const defaultButton = document.querySelector('[data-period="1M"]');
      if (defaultButton) {
        defaultButton.classList.add('bg-blue-50');
      }
    }
    
    // Setup stock ticker buttons
    function setupStockTickerButtons() {
      const stockTickerBtns = document.getElementById('stockTickerBtns');
      
      if (!stockTickerBtns) {
        console.log('Stock ticker buttons container not found');
        return;
      }
      
      // Get all buttons with data-ticker attribute
      const buttons = stockTickerBtns.querySelectorAll('[data-ticker]');
      console.log('Found ticker buttons:', buttons.length);
      
      buttons.forEach(button => {
        button.addEventListener('click', () => {
          const ticker = button.getAttribute('data-ticker');
          console.log('Stock ticker button clicked:', ticker);
          if (ticker) {
            currentStock = ticker;
            loadStockData();
            
            // Update active button styling
            buttons.forEach(b => b.classList.remove('bg-blue-100'));
            buttons.forEach(b => b.classList.add('bg-gray-100'));
            button.classList.remove('bg-gray-100');
            button.classList.add('bg-blue-100');
          }
        });
      });
      
      // Also set up the Load button if it exists
      const loadStockBtn = document.getElementById('loadStockBtn');
      if (loadStockBtn) {
        loadStockBtn.addEventListener('click', () => {
          const stockSearch = document.getElementById('stockSearch');
          if (stockSearch && stockSearch.value) {
            currentStock = stockSearch.value.toUpperCase();
            loadStockData();
            stockSearch.value = '';
            
            // Reset all button styling
            buttons.forEach(b => b.classList.remove('bg-blue-100'));
            buttons.forEach(b => b.classList.add('bg-gray-100'));
          }
        });
      }
    }
    
    // Initialize the main stock chart
    function initializeMainChart() {
      try {
        const chartContainer = document.getElementById('stockChart');
        if (!chartContainer) {
          console.error('Chart container not found');
          return;
        }
        
        // Create chart
        const chart = LightweightCharts.createChart(chartContainer, {
          width: chartContainer.clientWidth,
          height: chartContainer.clientHeight,
          layout: {
            backgroundColor: '#ffffff',
            textColor: '#333',
          },
          grid: {
            vertLines: { color: '#f0f3fa' },
            horzLines: { color: '#f0f3fa' },
          },
          crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
          },
          rightPriceScale: {
            borderColor: '#f0f3fa',
          },
          timeScale: {
            borderColor: '#f0f3fa',
          },
        });
        
        // Create candlestick series
        const candlestickSeries = chart.addCandlestickSeries({
          upColor: '#26a69a',
          downColor: '#ef5350',
          borderVisible: false,
          wickUpColor: '#26a69a',
          wickDownColor: '#ef5350',
        });
        
        // Create volume series
        const volumeSeries = chart.addHistogramSeries({
          color: '#26a69a',
          priceFormat: {
            type: 'volume',
          },
          priceScaleId: '',
          scaleMargins: {
            top: 0.8,
            bottom: 0,
          },
        });
        
        // Set data
        candlestickSeries.setData(stockData);
        
        const volumeData = stockData.map(item => ({
          time: item.time,
          value: item.volume,
          color: item.close > item.open ? '#26a69a' : '#ef5350',
        }));
        
        volumeSeries.setData(volumeData);
        
        // Handle window resize
        window.addEventListener('resize', () => {
          chart.applyOptions({
            width: chartContainer.clientWidth,
            height: chartContainer.clientHeight,
          });
        });
        
        // Store chart and series references
        mainChart = {
          chart,
          candlestickSeries,
          volumeSeries,
        };
        
        console.log('Main chart initialized successfully');
      } catch (error) {
        console.error('Error initializing main chart:', error);
      }
    }
    
    // Generate mock stock data
    function generateMockStockData(symbol, days) {
      const basePrice = getBasePrice(symbol);
      const data = [];
      const now = new Date();
      let price = basePrice;
      
      for (let i = days; i > 0; i--) {
        const date = new Date();
        date.setDate(now.getDate() - i);
        
        const volatility = 0.02; 
        const changePercent = (Math.random() * volatility * 2) - volatility;
        const change = price * changePercent;
        
        const open = price;
        const close = price + change;
        const high = Math.max(open, close) * (1 + Math.random() * 0.01);
        const low = Math.min(open, close) * (1 - Math.random() * 0.01);
        const volume = Math.floor(Math.random() * 10000000) + 1000000;
        
        data.push({
          time: date.toISOString().split('T')[0],
          open: open,
          high: high,
          low: low,
          close: close,
          volume: volume
        });
        
        price = close;
      }
      
      return data;
    }
    
    // Get base price for a stock symbol
    function getBasePrice(symbol) {
      const prices = {
        'AAPL': 150,
        'MSFT': 280,
        'GOOGL': 2200,
        'AMZN': 3300,
        'META': 300,
        'TSLA': 700,
        'NVDA': 240,
        'JPM': 140,
        'V': 220,
        'JNJ': 170
      };
      
      return prices[symbol] || 100 + Math.random() * 200;
    }
    
    // Update the stock information
    function updateStockInfo() {
      if (stockData.length < 2) return; // Ensure we have enough data points
      
      const lastDataPoint = stockData[stockData.length - 1];
      const prevDataPoint = stockData[stockData.length - 2];
      
      // Update the currentStockDisplay
      const currentStockDisplay = document.getElementById('currentStockDisplay');
      if (currentStockDisplay) currentStockDisplay.textContent = currentStock;
      
      // Update other elements if they exist
      const priceDiff = lastDataPoint.close - prevDataPoint.close;
      const percentDiff = (priceDiff / prevDataPoint.close) * 100;

      updateElementIfExists('stockPrice', '$' + lastDataPoint.close.toFixed(2));
      updateElementIfExists('stockPriceChange', `${priceDiff >= 0 ? '+' : ''}${priceDiff.toFixed(2)} (${percentDiff >= 0 ? '+' : ''}${percentDiff.toFixed(2)}%)`);
      updateElementIfExists('stockOpen', '$' + lastDataPoint.open.toFixed(2));
      updateElementIfExists('stockHigh', '$' + lastDataPoint.high.toFixed(2));
      updateElementIfExists('stockLow', '$' + lastDataPoint.low.toFixed(2));
      updateElementIfExists('stockVolume', lastDataPoint.volume.toLocaleString());
      
      // Update price change styling if element exists
      const priceChangeEl = document.getElementById('stockPriceChange');
      if (priceChangeEl) {
        if (priceDiff >= 0) {
          priceChangeEl.classList.remove('text-red-600');
          priceChangeEl.classList.add('text-green-600');
        } else {
          priceChangeEl.classList.remove('text-green-600');
          priceChangeEl.classList.add('text-red-600');
        }
      }
      
      // Update technical indicators
      calculateTechnicalIndicators();
    }
    
    // Helper function to update element content if it exists
    function updateElementIfExists(id, content) {
      const element = document.getElementById(id);
      if (element) element.textContent = content;
    }
    
    // Calculate technical indicators
    function calculateTechnicalIndicators() {
      if (stockData.length === 0) return;
      
      const lastDataPoint = stockData[stockData.length - 1];
      const lastPrice = lastDataPoint.close;
      
      // Calculate RSI
      const periods = 14;
      let gains = 0;
      let losses = 0;
      
      for (let i = Math.max(0, stockData.length - periods - 1); i < stockData.length - 1; i++) {
        const change = stockData[i + 1].close - stockData[i].close;
        if (change >= 0) {
          gains += change;
        } else {
          losses -= change;
        }
      }
      
      const avgGain = gains / periods;
      const avgLoss = losses / periods;
      const rs = avgLoss > 0 ? avgGain / avgLoss : 0;
      const rsi = 100 - (100 / (1 + rs));
      
      // Update RSI display if element exists
      updateElementIfExists('rsiValue', rsi.toFixed(2));
      
      // Update other indicator values with null checks
      updateElementIfExists('macdValue', (lastPrice * 0.02).toFixed(2)); // Simplified mock value
      updateElementIfExists('bbPrice', lastPrice.toFixed(2));
    }
    
    // Load stock data function
    function loadStockData() {
      // Generate new mock data for the selected stock
      stockData = generateMockStockData(currentStock, 90);
      
      // Update UI
      updateStockInfo();
      
      // Update chart if it exists
      if (mainChart) {
        mainChart.candlestickSeries.setData(stockData);
        
        const volumeData = stockData.map(item => ({
          time: item.time,
          value: item.volume,
          color: item.close > item.open ? '#26a69a' : '#ef5350',
        }));
        mainChart.volumeSeries.setData(volumeData);
      }
    }
    
    // Basic implementation for updateTimeframe
    function updateTimeframe() {
      // In a real app, we would adjust the data timeframe
      // For now, just reload the data
      loadStockData();
    }
  </script>
</body>
</html>
