/**
 * Loan Date Fix Patch
 * 
 * This file disables all JavaScript date overrides to prevent conflicts
 * with the fixed dates in the database.
 */

// Override the date override functions to use the actual dates from the database
export function fixLoanDate(loanName, dateString) {
  // Return the date as-is, no overrides
  return dateString;
}

export function getCorrectEndDate(loanName, startDate, termMonths) {
  // Calculate based on start date and term months
  try {
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(startDateObj);
    endDateObj.setMonth(startDateObj.getMonth() + termMonths);

    // Format as YYYY-MM-DD
    return endDateObj.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error calculating end date:', error);
    return '';
  }
}

export function applyLoanDateOverrides(loan) {
  // Return the loan as-is, no overrides
  return loan;
}

export function forceLoanDateUpdate(loanId, loanName, currentDate) {
  // Do nothing, dates are already fixed
  return Promise.resolve(true);
}

// Export empty overrides object
export const LOAN_START_DATES = {};
