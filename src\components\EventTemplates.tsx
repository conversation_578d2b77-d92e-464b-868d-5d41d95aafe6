import React, { useState, useEffect } from 'react';
import { localDataStore } from '../lib/local-data-store';
import { Modal } from './Modal';
import { ColorPicker } from './ColorPicker';
import { CreditCard, DollarSign, TrendingUp, PiggyBank, FileText, Shield, Plus, Edit, Trash2 } from 'lucide-react';

interface EventTemplate {
  id: string;
  name: string;
  icon?: string;
  title: string;
  type: string;
  amount?: number;
  description?: string;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  isRecurring?: boolean;
  recurrencePattern?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  reminder?: boolean;
  reminderTime?: number;
  reminderUnit?: 'minutes' | 'hours' | 'days';
  category?: string;
  created_at: string;
}

// Predefined categories for financial events
const eventCategories = [
  { id: 'bills', label: 'Bills & Utilities', icon: 'credit-card' },
  { id: 'income', label: 'Income & Salary', icon: 'dollar-sign' },
  { id: 'investments', label: 'Investments', icon: 'trending-up' },
  { id: 'savings', label: 'Savings', icon: 'piggy-bank' },
  { id: 'taxes', label: 'Taxes', icon: 'file-text' },
  { id: 'insurance', label: 'Insurance', icon: 'shield' },
  { id: 'debt', label: 'Debt Payments', icon: 'credit-card' },
  { id: 'education', label: 'Education', icon: 'book' },
  { id: 'healthcare', label: 'Healthcare', icon: 'activity' },
  { id: 'other', label: 'Other', icon: 'more-horizontal' },
];

interface EventTemplatesProps {
  onUseTemplate: (template: EventTemplate) => void;
  showToast: (message: string) => void;
}

export function EventTemplates({ onUseTemplate, showToast }: EventTemplatesProps) {
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  const [eventTemplates, setEventTemplates] = useState<EventTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<EventTemplate | null>(null);
  const [templateName, setTemplateName] = useState('');
  const [templateIcon, setTemplateIcon] = useState('');
  const [templateTitle, setTemplateTitle] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [templateAmount, setTemplateAmount] = useState('');
  const [templateColor, setTemplateColor] = useState('');
  const [isRecurring, setIsRecurring] = useState(false);
  const [recurrencePattern, setRecurrencePattern] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('monthly');
  const [reminder, setReminder] = useState(false);
  const [reminderTime, setReminderTime] = useState(30);
  const [reminderUnit, setReminderUnit] = useState<'minutes' | 'hours' | 'days'>('minutes');
  const [templateCategory, setTemplateCategory] = useState('');
  const [isEditingTemplate, setIsEditingTemplate] = useState(false);
  const [showTemplateLibrary, setShowTemplateLibrary] = useState(false);

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, []);

  // Load templates from local storage
  const loadTemplates = async () => {
    try {
      const templatesData = await localDataStore.data.loadCollection<EventTemplate>('event_templates') || [];
      
      // If no templates exist yet, create default templates
      if (templatesData.length === 0) {
        const defaultTemplates = createDefaultTemplates();
        await localDataStore.data.saveCollection('event_templates', defaultTemplates);
        setEventTemplates(defaultTemplates);
      } else {
        setEventTemplates(templatesData);
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      showToast('Error loading templates');
    }
  };

  // Create default event templates
  const createDefaultTemplates = (): EventTemplate[] => {
    const now = new Date().toISOString();
    return [
      {
        id: 'template-bill-payment',
        name: 'Monthly Bill Payment',
        icon: 'credit-card',
        title: 'Bill Payment',
        type: 'custom',
        description: 'Regular monthly bill payment',
        isRecurring: true,
        recurrencePattern: 'monthly',
        reminder: true,
        reminderTime: 2,
        reminderUnit: 'days',
        category: 'bills',
        created_at: now
      },
      {
        id: 'template-salary',
        name: 'Salary Deposit',
        icon: 'dollar-sign',
        title: 'Salary Payment',
        type: 'custom',
        amount: 0,
        description: 'Monthly salary deposit',
        backgroundColor: '#28a745',
        isRecurring: true,
        recurrencePattern: 'monthly',
        category: 'income',
        created_at: now
      },
      {
        id: 'template-investment',
        name: 'Investment Contribution',
        icon: 'trending-up',
        title: 'Investment Contribution',
        type: 'custom',
        amount: 0,
        description: 'Regular investment contribution',
        isRecurring: true,
        recurrencePattern: 'monthly',
        category: 'investments',
        created_at: now
      },
      {
        id: 'template-tax-payment',
        name: 'Quarterly Tax Payment',
        icon: 'file-text',
        title: 'Tax Payment',
        type: 'custom',
        amount: 0,
        description: 'Quarterly estimated tax payment',
        isRecurring: true,
        recurrencePattern: 'monthly',
        reminder: true,
        reminderTime: 7,
        reminderUnit: 'days',
        category: 'taxes',
        created_at: now
      }
    ];
  };

  // Save a template to local storage
  const saveTemplate = async () => {
    try {
      const template: EventTemplate = {
        id: isEditingTemplate && selectedTemplate ? selectedTemplate.id : `template-${Date.now()}`,
        name: templateName,
        icon: templateIcon,
        title: templateTitle,
        type: 'custom',
        amount: templateAmount ? parseFloat(templateAmount) : undefined,
        description: templateDescription,
        backgroundColor: templateColor,
        borderColor: templateColor,
        textColor: '#ffffff',
        isRecurring,
        recurrencePattern,
        reminder,
        reminderTime,
        reminderUnit,
        category: templateCategory,
        created_at: new Date().toISOString()
      };

      // If editing an existing template, update it
      if (isEditingTemplate && selectedTemplate) {
        const updatedTemplates = eventTemplates.map(t => 
          t.id === template.id ? template : t
        );
        await localDataStore.data.saveCollection('event_templates', updatedTemplates);
        setEventTemplates(updatedTemplates);
      } 
      // Otherwise add a new template
      else {
        const updatedTemplates = [...eventTemplates, template];
        await localDataStore.data.saveCollection('event_templates', updatedTemplates);
        setEventTemplates(updatedTemplates);
      }
      
      // Reset template form
      resetTemplateForm();
      setIsTemplateModalOpen(false);
      
      showToast('Template saved successfully');
    } catch (error) {
      console.error('Error saving template:', error);
      showToast('Error saving template');
    }
  };

  // Reset the template form
  const resetTemplateForm = () => {
    setTemplateName('');
    setTemplateIcon('');
    setTemplateTitle('');
    setTemplateDescription('');
    setTemplateAmount('');
    setTemplateColor('');
    setIsRecurring(false);
    setRecurrencePattern('monthly');
    setReminder(false);
    setReminderTime(30);
    setReminderUnit('minutes');
    setTemplateCategory('');
    setIsEditingTemplate(false);
    setSelectedTemplate(null);
  };
  
  // Delete a template
  const deleteTemplate = async (templateId: string) => {
    try {
      const updatedTemplates = eventTemplates.filter(t => t.id !== templateId);
      await localDataStore.data.saveCollection('event_templates', updatedTemplates);
      setEventTemplates(updatedTemplates);
      showToast('Template deleted successfully');
    } catch (error) {
      console.error('Error deleting template:', error);
      showToast('Error deleting template');
    }
  };

  // Edit a template
  const editTemplate = (template: EventTemplate) => {
    setSelectedTemplate(template);
    setTemplateName(template.name);
    setTemplateIcon(template.icon || '');
    setTemplateTitle(template.title);
    setTemplateDescription(template.description || '');
    setTemplateAmount(template.amount ? template.amount.toString() : '');
    setTemplateColor(template.backgroundColor || '');
    setIsRecurring(template.isRecurring || false);
    setRecurrencePattern(template.recurrencePattern || 'monthly');
    setReminder(template.reminder || false);
    setReminderTime(template.reminderTime || 30);
    setReminderUnit(template.reminderUnit || 'minutes');
    setTemplateCategory(template.category || '');
    setIsEditingTemplate(true);
    setIsTemplateModalOpen(true);
  };

  // Get icon component based on icon name
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'credit-card':
        return <CreditCard className="w-4 h-4" />;
      case 'dollar-sign':
        return <DollarSign className="w-4 h-4" />;
      case 'trending-up':
        return <TrendingUp className="w-4 h-4" />;
      case 'piggy-bank':
        return <PiggyBank className="w-4 h-4" />;
      case 'file-text':
        return <FileText className="w-4 h-4" />;
      case 'shield':
        return <Shield className="w-4 h-4" />;
      default:
        return <CreditCard className="w-4 h-4" />;
    }
  };

  return (
    <div>
      <div className="mb-4">
        <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Event Templates</h4>
        
        <div className="flex justify-between items-center mb-2">
          <button
            onClick={() => setShowTemplateLibrary(!showTemplateLibrary)}
            className="text-xs text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"
          >
            {showTemplateLibrary ? 'Hide Templates' : 'Show Templates'}
          </button>
          
          <button
            onClick={() => {
              resetTemplateForm();
              setIsTemplateModalOpen(true);
            }}
            className="flex items-center text-xs bg-indigo-600 text-white px-2 py-1 rounded hover:bg-indigo-700"
          >
            <Plus className="w-3 h-3 mr-1" /> New Template
          </button>
        </div>

        {showTemplateLibrary && (
          <div className="bg-gray-50 dark:bg-dark-800 rounded-md p-3 mb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {eventTemplates.map(template => (
                <div 
                  key={template.id} 
                  className="border rounded-md p-2 bg-white dark:bg-dark-700 dark:border-dark-600 flex justify-between items-center"
                >
                  <div 
                    className="flex items-center flex-grow cursor-pointer"
                    onClick={() => onUseTemplate(template)}
                  >
                    <div className="mr-2">
                      {getIconComponent(template.icon || 'credit-card')}
                    </div>
                    <div>
                      <div className="text-sm font-medium">{template.name}</div>
                      <div className="text-xs text-gray-500">
                        {template.isRecurring ? 'Recurring' : 'One-time'} • 
                        {eventCategories.find(c => c.id === template.category)?.label || 'Uncategorized'}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        editTemplate(template);
                      }}
                      className="text-gray-500 hover:text-indigo-600 mr-2"
                      title="Edit template"
                    >
                      <Edit className="w-4 h-4" />
                    </button>
                    
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        if (confirm('Are you sure you want to delete this template?')) {
                          deleteTemplate(template.id);
                        }
                      }}
                      className="text-gray-500 hover:text-red-600"
                      title="Delete template"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Template Modal */}
      <Modal
        isOpen={isTemplateModalOpen}
        onClose={() => setIsTemplateModalOpen(false)}
        title={isEditingTemplate ? 'Edit Template' : 'Create Event Template'}
      >
        <div className="p-4">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Template Name
            </label>
            <input
              type="text"
              value={templateName}
              onChange={(e) => setTemplateName(e.target.value)}
              placeholder="e.g., Monthly Rent Payment"
              className="w-full p-2 border border-gray-300 rounded-md"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Event Title
            </label>
            <input
              type="text"
              value={templateTitle}
              onChange={(e) => setTemplateTitle(e.target.value)}
              placeholder="e.g., Rent Payment"
              className="w-full p-2 border border-gray-300 rounded-md"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Amount (Optional)
            </label>
            <input
              type="number"
              value={templateAmount}
              onChange={(e) => setTemplateAmount(e.target.value)}
              placeholder="e.g., 1000"
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description (Optional)
            </label>
            <textarea
              value={templateDescription}
              onChange={(e) => setTemplateDescription(e.target.value)}
              placeholder="Add details about this event"
              className="w-full p-2 border border-gray-300 rounded-md"
              rows={3}
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              value={templateCategory}
              onChange={(e) => setTemplateCategory(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="">Select a category</option>
              {eventCategories.map(category => (
                <option key={category.id} value={category.id}>{category.label}</option>
              ))}
            </select>
          </div>

          <div className="mb-4">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="isRecurring"
                checked={isRecurring}
                onChange={(e) => setIsRecurring(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="isRecurring" className="text-sm font-medium text-gray-700">
                Recurring Event
              </label>
            </div>

            {isRecurring && (
              <div className="pl-6 border-l-2 border-gray-200">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Recurrence Pattern
                </label>
                <select
                  value={recurrencePattern}
                  onChange={(e) => setRecurrencePattern(e.target.value as any)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                  <option value="yearly">Yearly</option>
                </select>
              </div>
            )}
          </div>

          <div className="mb-4">
            <div className="flex items-center mb-2">
              <input
                type="checkbox"
                id="reminder"
                checked={reminder}
                onChange={(e) => setReminder(e.target.checked)}
                className="mr-2"
              />
              <label htmlFor="reminder" className="text-sm font-medium text-gray-700">
                Set Reminder
              </label>
            </div>

            {reminder && (
              <div className="pl-6 border-l-2 border-gray-200">
                <div className="flex items-center gap-2">
                  <div className="w-1/3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Remind me
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="60"
                      value={reminderTime}
                      onChange={(e) => setReminderTime(parseInt(e.target.value))}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    />
                  </div>
                  
                  <div className="w-2/3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Unit
                    </label>
                    <select
                      value={reminderUnit}
                      onChange={(e) => setReminderUnit(e.target.value as any)}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="minutes">Minutes before</option>
                      <option value="hours">Hours before</option>
                      <option value="days">Days before</option>
                    </select>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Custom Color (Optional)
            </label>
            <div className="flex items-center">
              <ColorPicker
                value={templateColor}
                onChange={setTemplateColor}
                colors={[
                  '#3788d8', // Default blue
                  '#28a745', // Green
                  '#dc3545', // Red
                  '#ffc107', // Yellow
                  '#6f42c1', // Purple
                  '#fd7e14', // Orange
                  '#20c997', // Teal
                  '#e83e8c', // Pink
                  '#6c757d', // Gray
                ]}
              />
              {templateColor && (
                <button
                  type="button"
                  onClick={() => setTemplateColor('')}
                  className="ml-2 text-xs text-gray-500 hover:text-gray-700"
                >
                  Clear
                </button>
              )}
            </div>
            {templateColor && (
              <div className="mt-2 flex items-center">
                <div
                  className="w-6 h-6 rounded mr-2"
                  style={{ backgroundColor: templateColor }}
                ></div>
                <span className="text-xs text-gray-500">Preview</span>
              </div>
            )}
          </div>

          <div className="flex justify-end">
            <button
              type="button"
              onClick={() => setIsTemplateModalOpen(false)}
              className="mr-3 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:bg-dark-600 dark:text-gray-200 dark:border-dark-500 dark:hover:bg-dark-700"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={saveTemplate}
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
            >
              {isEditingTemplate ? 'Update Template' : 'Save Template'}
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
