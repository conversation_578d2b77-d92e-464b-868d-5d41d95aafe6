/*
  # Add user_id to stocks table and create stock_history table

  1. Changes
    - Add user_id column to stocks table
    - Add foreign key constraint to auth.users
    - Add indexes for better query performance

  2. Security
    - Enable RLS on all tables
    - Add policies for user-specific data access
*/

-- Add user_id to stocks table
ALTER TABLE stocks
ADD COLUMN user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE;

-- Create index on user_id
CREATE INDEX idx_stocks_user_id ON stocks(user_id);

-- Update RLS policies for stocks
CREATE POLICY "Users can read own stocks"
  ON stocks
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own stocks"
  ON stocks
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own stocks"
  ON stocks
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Update RLS policies for stock_data
CREATE POLICY "Users can read own stock data"
  ON stock_data
  FOR SELECT
  TO authenticated
  USING (EXISTS (
    SELECT 1 FROM stocks
    WHERE stocks.id = stock_data.stock_id
    AND stocks.user_id = auth.uid()
  ));

CREATE POLICY "Users can insert own stock data"
  ON stock_data
  FOR INSERT
  TO authenticated
  WITH CHECK (EXISTS (
    SELECT 1 FROM stocks
    WHERE stocks.id = stock_data.stock_id
    AND stocks.user_id = auth.uid()
  ));