import { Line, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Scale,
  CoreScaleOptions
} from 'chart.js';
import { format, parseISO, differenceInDays } from 'date-fns';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { formatCurrency } from '../utils/currency';
import { PlusCircle } from 'lucide-react';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

// Types can be imported from local-data-store or from specific type files
// For now defining inline types to avoid import errors
interface Loan {
  id: string;
  name: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  remaining_balance: number;
  monthly_payment: number;
  next_payment_date: string;
  created_at: string;
}

interface CD {
  id: string;
  institution: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  maturity_date: string;
}

interface BankAccount {
  id: string;
  bank_name: string;
  account_type: string;
  balance: number;
}

interface FinancialGoal {
  id: string;
  name: string;
  target_amount: number;
  current_amount: number;
  target_date: string;
}

export function Dashboard() {
  const { data: loans } = useSupabaseQuery<Loan>('loans');
  const { data: cds } = useSupabaseQuery<CD>('cds');
  const { data: bankAccounts } = useSupabaseQuery<BankAccount>('bank_accounts');
  const { data: goals } = useSupabaseQuery<FinancialGoal>('financial_goals');

  // Use the actual remaining_balance from the database instead of recalculating
  const totalLoanBalance = loans?.reduce((sum, loan) => sum + loan.remaining_balance, 0) || 0;
  const totalCDValue = cds?.reduce((sum, cd) => sum + cd.principal, 0) || 0;
  const totalMonthlyEarnings = cds?.reduce((sum, cd) =>
    sum + (cd.principal * (cd.interest_rate / 100) / 12), 0) || 0;
  const totalBankBalance = bankAccounts?.reduce((sum, account) => sum + account.balance, 0) || 0;
  const totalAssets = totalBankBalance + totalCDValue;
  const netWorth = totalAssets - totalLoanBalance;

  // Calculate month-over-month changes (mock data for demonstration)
  const monthlyChanges = {
    assets: 5.2,
    liabilities: -2.8,
    netWorth: 8.5,
    income: 3.2
  };

  const spendingData = {
    labels: Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return format(date, 'MMM d');
    }),
    datasets: [
      {
        label: 'Daily Spending',
        data: [120, 85, 150, 95, 180, 140, 110],
        borderColor: 'rgb(99, 102, 241)',
        backgroundColor: 'rgba(99, 102, 241, 0.1)',
        tension: 0.4,
        fill: true
      },
    ],
  };

  const assetAllocationData = {
    labels: ['Bank Accounts', 'CDs', 'Investments'],
    datasets: [
      {
        data: [totalBankBalance, totalCDValue, 0], // Add investment value when available
        backgroundColor: [
          'rgba(99, 102, 241, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)'
        ],
        borderWidth: 0
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Spending Trend (Last 7 Days)',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          callback: function(this: Scale<CoreScaleOptions>, tickValue: string | number) {
            return formatCurrency(Number(tickValue));
          }
        }
      }
    }
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      title: {
        display: true,
        text: 'Asset Allocation',
      },
    },
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Financial Dashboard</h1>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Last updated: {format(new Date(), 'MMM d, yyyy h:mm a')}
          </p>
        </div>
        <div className="flex items-center gap-2 bg-blue-50 dark:bg-blue-900/20 px-4 py-2 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-center gap-1">
            <PlusCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
              {goals?.length || 0} Active Goals
            </span>
          </div>
        </div>
      </div>

      {/* Net Worth Summary - Modern Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 dark:from-emerald-600 dark:to-emerald-700 p-6 rounded-lg shadow-sm text-white">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-emerald-100 font-medium text-sm">Net Worth</h3>
            <div className="p-2 bg-emerald-400/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-emerald-100" />
            </div>
          </div>
          <p className="text-3xl font-bold mb-1">{formatCurrency(netWorth)}</p>
          <div className="flex items-center text-sm">
            {monthlyChanges.netWorth > 0 ? (
              <span className="text-emerald-200">↗ +{monthlyChanges.netWorth}% this month</span>
            ) : (
              <span className="text-red-200">↘ {monthlyChanges.netWorth}% this month</span>
            )}
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 p-6 rounded-lg shadow-sm text-white">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-blue-100 font-medium text-sm">Total Assets</h3>
            <div className="p-2 bg-blue-400/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-blue-100" />
            </div>
          </div>
          <p className="text-3xl font-bold mb-1">{formatCurrency(totalAssets)}</p>
          <div className="flex items-center text-sm">
            {monthlyChanges.assets > 0 ? (
              <span className="text-blue-200">↗ +{monthlyChanges.assets}% this month</span>
            ) : (
              <span className="text-red-200">↘ {monthlyChanges.assets}% this month</span>
            )}
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-500 to-purple-600 dark:from-purple-600 dark:to-purple-700 p-6 rounded-lg shadow-sm text-white">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-purple-100 font-medium text-sm">Total Liabilities</h3>
            <div className="p-2 bg-purple-400/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-purple-100" />
            </div>
          </div>
          <p className="text-3xl font-bold mb-1">{formatCurrency(totalLoanBalance)}</p>
          <div className="flex items-center text-sm">
            {monthlyChanges.liabilities < 0 ? (
              <span className="text-rose-200">↘ -{Math.abs(monthlyChanges.liabilities)}% this month</span>
            ) : (
              <span className="text-red-200">↗ +{Math.abs(monthlyChanges.liabilities)}% this month</span>
            )}
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Bank Balance</h3>
            <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <p className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
            {formatCurrency(totalBankBalance)}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">Across all accounts</p>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">CD Portfolio</h3>
            <div className="p-2 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
            </div>
          </div>
          <p className="text-2xl font-bold text-emerald-600 dark:text-emerald-400 mb-1">
            {formatCurrency(totalCDValue)}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">Total invested</p>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Monthly Income</h3>
            <div className="p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <p className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
            {formatCurrency(totalMonthlyEarnings)}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">Passive income</p>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">Loan Balance</h3>
            <div className="p-2 bg-rose-50 dark:bg-rose-900/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-rose-600 dark:text-rose-400" />
            </div>
          </div>
          <p className="text-2xl font-bold text-rose-600 dark:text-rose-400 mb-1">
            {formatCurrency(totalLoanBalance)}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">Total debt</p>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Spending Trend</h3>
          <div style={{ height: '300px' }}>
            <Line options={chartOptions} data={spendingData} />
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Asset Allocation</h3>
          <div style={{ height: '300px' }}>
            <Doughnut options={doughnutOptions} data={assetAllocationData} />
          </div>
        </div>
      </div>

      {/* Account Details */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">💳 Bank Accounts</h3>
          <div className="space-y-4">
            {bankAccounts?.map(account => (
              <div key={account.id} className="flex justify-between items-center pb-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                <div>
                  <p className="font-medium text-gray-800 dark:text-gray-200">{account.bank_name}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">{account.account_type}</p>
                </div>
                <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                  {formatCurrency(account.balance)}
                </p>
              </div>
            ))}
            {bankAccounts?.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">No bank accounts</p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">Add your first account to get started</p>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">🏠 Active Loans</h3>
          <div className="space-y-4">
            {loans?.map(loan => (
              <div key={loan.id} className="flex justify-between items-center pb-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                <div>
                  <p className="font-medium text-gray-800 dark:text-gray-200">{loan.name}</p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Next payment: {format(parseISO(loan.next_payment_date), 'MMM d')}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-semibold text-rose-600 dark:text-rose-400">
                    {formatCurrency(loan.remaining_balance)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {formatCurrency(loan.monthly_payment)}/mo
                  </p>
                </div>
              </div>
            ))}
            {loans?.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">No active loans</p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">Great! You're debt-free</p>
              </div>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">💰 Active CDs</h3>
            <div className="text-right">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Monthly Earnings</p>
              <p className="text-lg font-semibold text-emerald-600 dark:text-emerald-400">
                {formatCurrency(totalMonthlyEarnings)}
              </p>
            </div>
          </div>
          <div className="space-y-4">
            {cds?.map(cd => {
              const daysToMaturity = differenceInDays(new Date(cd.maturity_date), new Date());
              return (
                <div key={cd.id} className="flex justify-between items-center pb-3 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                  <div>
                    <p className="font-medium text-gray-800 dark:text-gray-200">{cd.institution}</p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {daysToMaturity} days to maturity
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-semibold text-emerald-600 dark:text-emerald-400">{cd.interest_rate}% APY</p>
                    <p className="text-sm text-emerald-600 dark:text-emerald-400">
                      {formatCurrency((cd.principal * cd.interest_rate / 100) / 12)}/mo
                    </p>
                  </div>
                </div>
              );
            })}
            {cds?.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">No active CDs</p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-1">Consider investing in CDs for steady income</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}