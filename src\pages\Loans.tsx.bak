import React, { useState, useEffect, useMemo } from 'react';
import { Plus<PERSON>ircle, Pencil, Trash2, ChevronDown, ChevronUp } from 'lucide-react';
import { Modal } from '../components/Modal';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { useAuthStore } from '../store/localAuthStore';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { Loan } from '../types/index';
import { format, isBefore } from 'date-fns';
import { formatCurrency } from '../utils/currency';
import {
  calculateMonthlyPayment,
  calculateAmortizationSchedule,
  AmortizationRow,
  clearScheduleCache
} from '../utils/loanCalculator';
import {
  parseDate,
  formatDateString,
  addMonthsToDate,
  calculateMonthsBetween
} from '../utils/dateUtils';
// @ts-ignore - Ignore TypeScript errors for this import
import { fixedHandleEdit, fixedHandleSubmit } from '../utils/loanEditFunctionPatch';

/**
 * Loans component for managing loans
 */
export function Loans() {
  // State
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null);
  const [expandedLoanId, setExpandedLoanId] = useState<string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);

  // Form state
  const [name, setName] = useState('');
  const [principal, setPrincipal] = useState('');
  const [interestRate, setInterestRate] = useState('');
  const [startDate, setStartDate] = useState(formatDateString(new Date()));
  const [endDate, setEndDate] = useState(formatDateString(addMonthsToDate(new Date(), 12)));
  const [monthlyPayment, setMonthlyPayment] = useState('');
  const [processingForm, setProcessingForm] = useState(false);

  // Auth
  const user = useAuthStore((state) => state.user);

  // Data
  const {
    data: loans,
    loading,
    addItem,
    deleteItem,
    updateItem,
    refetch
  } = useSupabaseQuery<Loan>('loans', {
    orderBy: { column: 'created_at', ascending: false },
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Calculate monthly payment whenever form inputs change
  useEffect(() => {
    if (principal && interestRate && startDate && endDate) {
      try {
        const start = parseDate(startDate);
        const end = parseDate(endDate);

        if (!start || !end) {
          setMonthlyPayment('');
          return;
        }

        const termMonths = calculateMonthsBetween(start, end);

        if (termMonths <= 0) {
          setMonthlyPayment('');
          return;
        }

        const payment = calculateMonthlyPayment(
          Number(principal),
          Number(interestRate),
          termMonths
        );

        setMonthlyPayment(payment.toFixed(2));
      } catch (error) {
        console.error('Error calculating monthly payment:', error);
        setMonthlyPayment('');
      }
    } else {
      setMonthlyPayment('');
    }
  }, [principal, interestRate, startDate, endDate]);

  // Update loans with past due dates
  useEffect(() => {
    const updatePastDueLoans = async () => {
      if (!loans || loans.length === 0) return;

      const now = new Date();
      const loansToUpdate = loans.filter(loan => {
        const nextPaymentDate = parseDate(loan.next_payment_date);
        return nextPaymentDate && isBefore(nextPaymentDate, now);
      });

      if (loansToUpdate.length === 0) return;

      for (const loan of loansToUpdate) {
        try {
          const nextPaymentDate = parseDate(loan.next_payment_date);
          if (!nextPaymentDate) continue;

          // Calculate remaining balance only, don't update next_payment_date
          const schedule = calculateAmortizationSchedule(
            loan.principal,
            loan.interest_rate,
            loan.term_months,
            parseDate(loan.next_payment_date) || new Date(),
            loan.name,
            true
          );

          // Find current position in amortization schedule
          const currentScheduleItem = schedule.find(item =>
            item.dueDate > now
          ) || schedule[schedule.length - 1];

          const newBalance = currentScheduleItem?.closingPrincipal || loan.remaining_balance;

          // Update only the remaining balance, not the next_payment_date
          await updateItem(loan.id, {
            remaining_balance: Number(newBalance)
          });

        } catch (error) {
          console.error(`Error updating loan ${loan.id}:`, error);
        }
      }

      // Reload data after updates
      refetch();
    };

    updatePastDueLoans();
  }, [loans, updateItem, refetch]);

  // Calculate amortization schedules for all loans
  const loanSchedules = useMemo(() => {
    const schedules: Record<string, AmortizationRow[]> = {};

    if (loans) {
      loans.forEach(loan => {
        try {
          // Get start date
          const startDate = parseDate(loan.next_payment_date);
          if (!startDate) return;

          // Get end date if available
          let endDate: Date | undefined = undefined;
          if ('end_date' in loan && loan.end_date) {
            const parsedDate = parseDate(loan.end_date);
            if (parsedDate) {
              endDate = parsedDate;
            }
          }

          schedules[loan.id] = calculateAmortizationSchedule(
            loan.principal,
            loan.interest_rate,
            loan.term_months,
            startDate,
            loan.name || "unknown", // Provide a default string value
            true,  // Use cache
            endDate // Pass end date if available
          );
        } catch (error) {
          console.error(`Error calculating schedule for loan ${loan.id}:`, error);
          schedules[loan.id] = [];
        }
      });
    }

    return schedules;
  }, [loans]);

  // Fix all loan timelines in storage when component loads - only once
  useEffect(() => {
    if (loading || !loans) return;

    // Use a ref to track if we've already run the fix
    const hasRunFix = React.useRef(false);

    // Only run once
    if (hasRunFix.current) return;
    hasRunFix.current = true;

    // We're disabling the automatic date fixing to allow manual date editing
    console.log("Automatic loan date fixing is disabled to allow manual date editing");
    
    /* Original code commented out to prevent overriding user-defined dates
    // Check if any loans have dates that need fixing
    const loansWithWrongDates = loans.filter(loan =>
      // Check for 2025 dates or missing end_date
      (loan.next_payment_date && loan.next_payment_date.startsWith('2025-')) ||
      !loan.end_date
    );

    if (loansWithWrongDates.length > 0) {
      // Fix each loan's timeline in storage
      Promise.all(
        loansWithWrongDates.map(loan =>
          forceLoanDateUpdate(loan.id, loan.name, loan.next_payment_date)
        )
      ).then(results => {
        const successCount = results.filter(Boolean).length;

        // Refresh the data if any timelines were fixed
        if (successCount > 0) {
          setTimeout(() => {
            refetch();
          }, 500);
        }
      });
    }
    */
  }, []);

  // Load loans for display
  useEffect(() => {
    if (loading || !loans) return;

    // Map loans for display and calculation
    loans.map((loan: Loan) => {
      // Use the original loan without applying any date overrides
      const loanCopy = { ...loan }; // Use the original loan data

      // Calculate remaining balance if needed
      if (!loanCopy.remaining_balance || loanCopy.remaining_balance === 0) {
        loanCopy.remaining_balance = loanCopy.principal;
      }

      // Calculate monthly payment if needed
      if (!loanCopy.monthly_payment || loanCopy.monthly_payment === 0) {
        loanCopy.monthly_payment = calculateMonthlyPayment(
          loanCopy.principal,
          loanCopy.interest_rate,
          loanCopy.term_months
        );
      }

      return loanCopy;
    });

    // No need to set any state here, just processing the loans

  }, [loans, loading]);

  /**
   * Handle form submission for adding or editing loans
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError(null);
    setProcessingForm(true);

    try {
      // Validate inputs
      if (!name || !principal || !interestRate || !startDate || !endDate) {
        setFormError('All fields are required');
        setProcessingForm(false);
        return;
      }

      // Parse dates for validation only - accept any valid date format
      console.log("Validating dates:", startDate, endDate);
      const start = parseDate(startDate);
      const end = parseDate(endDate);

      if (!start || !end) {
        setFormError('Invalid dates. Please use YYYY-MM-DD format.');
        setProcessingForm(false);
        return;
      }

      console.log("Parsed dates:", start, end);

      // Calculate term in months
      const termMonths = calculateMonthsBetween(start, end);
      console.log("Calculated term months:", termMonths);

      if (termMonths <= 0) {
        setFormError('End date must be after start date');
        setProcessingForm(false);
        return;
      }

      // Use the fixed submit function
      const formData = {
        selectedLoan,
        name,
        principal,
        interestRate,
        startDate,
        endDate,
        termMonths,
        monthlyPayment
      };

      console.log("Submitting form data:", formData);

      const callbacks = {
        updateItem,
        addItem,
        setIsModalOpen,
        resetForm,
        refetch
      };

      // Call the fixed submit function and await its result
      const success = await fixedHandleSubmit(formData, callbacks);

      if (!success) {
        setFormError('An error occurred. Please try again.');
      }
    } catch (error) {
      console.error('Error saving loan:', error);
      setFormError('An error occurred. Please try again.');
    } finally {
      setProcessingForm(false);
    }
  };

  /**
   * Open edit form with loan data
   */
  const handleEdit = (loan: Loan) => {
    try {
      // Use the fixed edit function
      const callbacks = {
        setSelectedLoan,
        setName,
        setPrincipal,
        setInterestRate,
        setStartDate,
        setEndDate,
        setMonthlyPayment,
        setFormError,
        setIsModalOpen
      };

      fixedHandleEdit(loan, callbacks);
    } catch (error) {
      console.error('Error preparing loan edit:', error);
      alert('Error loading loan data. Please try again.');
    }
  };

  /**
   * Handle loan deletion
   */
  const handleDelete = (loan: Loan) => {
    setSelectedLoan(loan);
    setIsDeleteModalOpen(true);
  };

  /**
   * Confirm and execute loan deletion
   */
  const confirmDelete = async () => {
    if (!selectedLoan) return;

    try {
      // Clear any cached schedules for this loan
      const startDate = parseDate(selectedLoan.next_payment_date);
      if (startDate) {
        const cacheKey = `${selectedLoan.principal}-${selectedLoan.interest_rate}-${selectedLoan.term_months}-${startDate.getTime()}`;
        clearScheduleCache(cacheKey);
      }

      // Delete the loan
      await deleteItem(selectedLoan.id);
      setIsDeleteModalOpen(false);
      setSelectedLoan(null);
    } catch (error) {
      console.error('Error deleting loan:', error);
      alert('Error deleting loan. Please try again.');
    }
  };

  /**
   * Reset form state
   */
  const resetForm = () => {
    setSelectedLoan(null);
    setName('');
    setPrincipal('');
    setInterestRate('');
    setStartDate(formatDateString(new Date())); // Default to today
    setEndDate(formatDateString(addMonthsToDate(new Date(), 12))); // Default to 1 year from now
    setMonthlyPayment('');
    setFormError(null);
    setProcessingForm(false);
  };

  /**
   * Toggle loan details view
   */
  const toggleLoanDetails = (loanId: string) => {
    setExpandedLoanId(expandedLoanId === loanId ? null : loanId);
  };

  // Loading state
  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Loan Management</h1>
        <button
          onClick={() => {
            resetForm();
            setIsModalOpen(true);
          }}
          className="flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700"
        >
          <PlusCircle className="w-5 h-5 mr-2" />
          Add Loan
        </button>
      </div>

      {/* Modal for adding/editing loans */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          if (!processingForm) {
            setIsModalOpen(false);
            resetForm();
          }
        }}
        title={selectedLoan ? "Edit Loan" : "Add New Loan"}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Loan Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Loan Name
            </label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
              disabled={processingForm}
            />
          </div>

          {/* Principal Amount */}
          <div>
            <label htmlFor="principal" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Principal Amount
            </label>
            <input
              type="number"
              id="principal"
              value={principal}
              onChange={(e) => setPrincipal(e.target.value)}
              min="0"
              step="0.01"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
              disabled={processingForm}
            />
          </div>

          {/* Interest Rate */}
          <div>
            <label htmlFor="interestRate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Interest Rate (%)
            </label>
            <input
              type="number"
              id="interestRate"
              value={interestRate}
              onChange={(e) => setInterestRate(e.target.value)}
              min="0"
              step="0.01"
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
              required
              disabled={processingForm}
            />
          </div>

          {/* Start Date */}
          <div>
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Start Date
            </label>
            <div className="relative">
              <input
                type="date"
                id="startDate"
                value={startDate}
                onChange={(e) => {
                  // Accept any valid date input without restrictions
                  const inputDate = e.target.value;
                  console.log("Start date input:", inputDate);
                  setStartDate(inputDate);
                }}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                required
                disabled={processingForm}
              />
            </div>
          </div>

          {/* End Date */}
          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              End Date
            </label>
            <div className="relative">
              <input
                type="date"
                id="endDate"
                value={endDate}
                onChange={(e) => {
                  // Accept any valid date input without restrictions
                  const inputDate = e.target.value;
                  console.log("End date input:", inputDate);
                  setEndDate(inputDate);
                }}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                required
                disabled={processingForm}
              />
            </div>
          </div>

          {/* Monthly Payment (Calculated) */}
          <div>
            <label htmlFor="monthlyPayment" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Monthly Payment (Calculated)
            </label>
            <input
              type="text"
              id="monthlyPayment"
              value={monthlyPayment ? formatCurrency(Number(monthlyPayment)) : ''}
              readOnly
              className="mt-1 block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>

          {/* Error message */}
          {formError && (
            <div className="text-red-600 text-sm p-2 border border-red-200 rounded bg-red-50 dark:bg-red-900/20">
              {formError}
            </div>
          )}

          {/* Form buttons */}
          <div className="flex justify-end gap-3 mt-6">
            <button
              type="button"
              onClick={() => {
                if (!processingForm) {
                  setIsModalOpen(false);
                  resetForm();
                }
              }}
              className="px-4 py-2 border rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-dark-700"
              disabled={processingForm}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50"
              disabled={processingForm}
            >
              {processingForm ? "Processing..." : (selectedLoan ? "Save Changes" : "Add Loan")}
            </button>
          </div>
        </form>
      </Modal>

      {/* Delete confirmation dialog */}
      <ConfirmDialog
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Loan"
        message={`Are you sure you want to delete the loan "${selectedLoan?.name}"? This action cannot be undone.`}
      />

      {/* Loans grid */}
      <div className="grid gap-6">
        {loans && loans.length > 0 ? (
          loans.map((loan) => {
            const progressPercentage = ((loan.principal - loan.remaining_balance) / loan.principal) * 100;
            const endDate = addMonthsToDate(parseDate(loan.next_payment_date) || new Date(), loan.term_months);

            // Use precomputed schedule or generate on demand
            const amortizationSchedule = loanSchedules[loan.id] || [];

            return (
              <div key={loan.id} className="bg-white dark:bg-dark-800 rounded-lg shadow-sm">
                {/* Loan header */}
                <div className="p-6">
                  <div className="flex justify-between items-start mb-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">{loan.name}</h3>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        {loan.term_months} months @ {loan.interest_rate}% APR
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleEdit(loan)}
                        className="p-2 text-gray-600 dark:text-gray-300 hover:text-indigo-600 rounded-full hover:bg-gray-100 dark:hover:bg-dark-700"
                        aria-label="Edit loan"
                      >
                        <Pencil className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDelete(loan)}
                        className="p-2 text-gray-600 dark:text-gray-300 hover:text-red-600 rounded-full hover:bg-gray-100 dark:hover:bg-dark-700"
                        aria-label="Delete loan"
                      >
                        <Trash2 className="h-5 w-5" />
                      </button>
                    </div>
                  </div>

                  {/* Loan details */}
                  <div className="space-y-4">
                    {/* Progress bar */}
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="font-medium text-gray-800 dark:text-gray-200">Repayment Progress</span>
                        <span className="font-medium text-gray-800 dark:text-gray-200">{progressPercentage.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-dark-600 rounded-full h-2.5">
                        <div
                          className="bg-green-500 h-2.5 rounded-full"
                          style={{ width: `${Math.min(100, Math.max(0, progressPercentage))}%` }}
                        ></div>
                      </div>
                    </div>

                    {/* Main loan details in a grid */}
                    <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
                      <div>
                        <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">Monthly Payment</h4>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {formatCurrency(Number(loan.monthly_payment.toFixed(2)))}
                        </p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">Next Payment Date</h4>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {format(new Date(loan.next_payment_date), 'MMM d, yyyy')}
                        </p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">Original Principal</h4>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {formatCurrency(loan.principal)}
                        </p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-gray-800 dark:text-gray-200">End Date</h4>
                        <p className="text-lg font-semibold text-gray-900 dark:text-white">
                          {format(endDate, 'MMM d, yyyy')}
                        </p>
                      </div>
                    </div>

                    {/* Toggle amortization schedule button */}
                    <div>
                      <button
                        onClick={() => toggleLoanDetails(loan.id)}
                        className="flex items-center text-sm font-medium text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300"
                      >
                        {expandedLoanId === loan.id ? (
                          <>
                            <ChevronUp className="w-4 h-4 mr-1" />
                            Hide Amortization Schedule
                          </>
                        ) : (
                          <>
                            <ChevronDown className="w-4 h-4 mr-1" />
                            Show Amortization Schedule
                          </>
                        )}
                      </button>
                    </div>

                    {/* Collapsible amortization schedule */}
                    {expandedLoanId === loan.id && (
                      <div className="mt-4 overflow-x-auto">
                        <div className="inline-block min-w-full align-middle">
                          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead>
                              <tr>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-dark-700">
                                  Due Date
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-dark-700">
                                  Installment
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-dark-700">
                                  Interest
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-dark-700">
                                  Principal
                                </th>
                                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-dark-700">
                                  Closing Balance
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-700">
                              {/* Only show first 12 rows for performance */}
                              {amortizationSchedule.slice(0, 12).map((row, index) => (
                                <tr
                                  key={index}
                                  className={new Date() > row.dueDate ? "text-gray-500 dark:text-gray-400" : ""}
                                >
                                  <td className="px-4 py-2 whitespace-nowrap text-sm">
                                    {format(row.dueDate, 'MMM d, yyyy')}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm font-medium">
                                    {formatCurrency(row.installment)}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm">
                                    {formatCurrency(row.interestComponent)}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm">
                                    {formatCurrency(row.principalComponent)}
                                  </td>
                                  <td className="px-4 py-2 whitespace-nowrap text-sm">
                                    {formatCurrency(row.closingPrincipal)}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        ) : (
          <div className="text-center py-16 bg-white dark:bg-dark-800 rounded-lg shadow-sm">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">No Loans Found</h3>
            <p className="mt-2 text-gray-500 dark:text-gray-400">
              Get started by adding your first loan.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}