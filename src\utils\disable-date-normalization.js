/**
 * Disable Date Normalization
 *
 * This file patches the application to disable all automatic date normalization
 * and allow direct editing of loan dates.
 */

// Override the date fixer functions with pass-through versions
import { addMonkeyPatch } from './monkey-patch';
import * as loanDateFixer from './loanDateFixerDisabled.ts';
import * as loanEditFunctionPatch from './loanEditFunctionPatch.js';

// Apply the patches when this module is imported
console.log('[PATCH] Applying date normalization disabler patch');

// Patch 3: Add a function to clear all loan caches
function clearAllLoanCaches() {
  console.log('[PATCH] Clearing all loan caches');

  // Clear all localStorage items that might be caching loan data
  if (typeof localStorage !== 'undefined') {
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('loan_') || key.includes('cache')) {
        console.log(`[PATCH] Clearing cache: ${key}`);
        localStorage.removeItem(key);
      }
    });
  }

  // Clear any in-memory caches
  if (typeof window !== 'undefined' && window.__LOAN_CACHE__) {
    console.log('[PATCH] Clearing window.__LOAN_CACHE__');
    window.__LOAN_CACHE__ = {};
  }

  console.log('[PATCH] All loan caches cleared');
  return true;
}

// Function to clear all caches when the module is loaded
function clearAllCachesOnLoad() {
  console.log('[PATCH] Clearing all caches on application load');

  // Wait for DOM to be ready
  if (typeof document !== 'undefined') {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        setTimeout(clearAllLoanCaches, 500);
      });
    } else {
      // DOM already ready, clear caches now
      setTimeout(clearAllLoanCaches, 500);
    }

    // Also clear caches when the Loans page is loaded
    if (typeof window !== 'undefined') {
      window.addEventListener('hashchange', () => {
        if (window.location.hash.includes('loans') || window.location.pathname.includes('loans')) {
          console.log('[PATCH] Loans page detected, clearing caches');
          clearAllLoanCaches();
        }
      });
    }
  }
}

// Patch 1: Disable the loan date fixer
try {
  // Override the fixLoanDate function to be a pass-through
  addMonkeyPatch(loanDateFixer, 'fixLoanDate', function(loanName, dateString) {
    console.log(`[PATCH] fixLoanDate called for ${loanName}, returning original date: ${dateString}`);
    return dateString;
  });

  // Override the applyLoanDateOverrides function to be a pass-through
  addMonkeyPatch(loanDateFixer, 'applyLoanDateOverrides', function(loan) {
    console.log(`[PATCH] applyLoanDateOverrides called for ${loan.name}, returning original loan`);
    return loan;
  });

  // Override the forceLoanDateUpdate function to be a no-op
  addMonkeyPatch(loanDateFixer, 'forceLoanDateUpdate', function(_loanId, loanName, _currentDate) {
    console.log(`[PATCH] forceLoanDateUpdate called for ${loanName}, doing nothing`);
    return Promise.resolve(true);
  });

  console.log('[PATCH] Successfully patched loanDateFixerDisabled');
} catch (error) {
  console.error('[PATCH] Failed to patch loanDateFixerDisabled:', error);
}

// Patch 2: Disable the loan edit function patch
try {
  // Override the fixedHandleEdit function to preserve dates
  addMonkeyPatch(loanEditFunctionPatch, 'fixedHandleEdit', function(loan, callbacks) {
    console.log(`[PATCH] fixedHandleEdit called for ${loan.name}, preserving original dates`);

    // Extract callbacks
    const {
      setSelectedLoan,
      setName,
      setPrincipal,
      setInterestRate,
      setStartDate,
      setEndDate,
      setMonthlyPayment,
      setFormError,
      setIsModalOpen
    } = callbacks;

    // Make a deep copy of the loan to avoid reference issues
    const fixedLoan = JSON.parse(JSON.stringify(loan));

    // Log the loan data for debugging
    console.log('[PATCH] Original loan data:', loan);

    // Set basic loan properties
    setSelectedLoan(fixedLoan);
    setName(fixedLoan.name);
    setPrincipal(fixedLoan.principal.toString());
    setInterestRate(fixedLoan.interest_rate.toString());

    // Set dates directly without any overrides
    console.log(`[PATCH] Setting start date for edit: ${fixedLoan.next_payment_date}`);
    console.log(`[PATCH] Setting end date for edit: ${fixedLoan.end_date}`);
    setStartDate(fixedLoan.next_payment_date);
    setEndDate(fixedLoan.end_date);

    // Set monthly payment
    setMonthlyPayment(fixedLoan.monthly_payment.toFixed(2));

    // Reset any previous errors
    setFormError(null);

    // Open modal
    setIsModalOpen(true);

    return true;
  });

  // Override the fixedHandleSubmit function to preserve dates
  addMonkeyPatch(loanEditFunctionPatch, 'fixedHandleSubmit', function(formData, callbacks) {
    console.log(`[PATCH] fixedHandleSubmit called, preserving user-entered dates`);

    // Extract form data
    const {
      selectedLoan,
      name,
      principal,
      interestRate,
      startDate,
      endDate,
      termMonths
    } = formData;

    // Extract callbacks
    const {
      updateItem,
      addItem,
      setIsModalOpen,
      resetForm,
      refetch
    } = callbacks;

    // Log the form data for debugging
    console.log('[PATCH] Form data for submit:', formData);

    // Create the loan object with the date strings from the form
    let loanData = {
      name,
      principal: Number(principal),
      interest_rate: Number(interestRate),
      term_months: termMonths,
      next_payment_date: startDate,
      end_date: endDate,
      // Preserve remaining balance if updating
      remaining_balance: selectedLoan ? selectedLoan.remaining_balance : Number(principal)
    };

    // Log the loan data before saving
    console.log('[PATCH] Loan data to save:', loanData);

    // Update or create loan
    if (selectedLoan) {
      console.log('[PATCH] Updating loan with ID:', selectedLoan.id);

      // Create a complete update object by merging with the original loan
      const updateData = {
        ...selectedLoan,
        ...loanData
      };

      console.log('[PATCH] Complete update data:', updateData);

      // Update the loan with direct date string
      updateItem(selectedLoan.id, updateData)
        .then(updatedLoan => {
          console.log('[PATCH] Loan updated successfully:', updatedLoan);

          // Force clear cache for this loan
          if (typeof localStorage !== 'undefined') {
            localStorage.removeItem(`loan_${selectedLoan.id}_cache`);
          }

          // Force a full cache clear to ensure all data is refreshed
          clearAllLoanCaches();
        })
        .catch(error => {
          console.error('[PATCH] Error updating loan:', error);
        });
    } else {
      console.log('[PATCH] Creating new loan with data:', loanData);

      // Create new loan with direct date string
      addItem({
        user_id: 'default-user',
        ...loanData,
        remaining_balance: Number(principal)
      })
        .then(newLoan => {
          console.log('[PATCH] Loan created successfully:', newLoan);

          // Force a full cache clear to ensure all data is refreshed
          clearAllLoanCaches();
        })
        .catch(error => {
          console.error('[PATCH] Error creating loan:', error);
        });
    }

    // Success - close modal and reset form
    setIsModalOpen(false);
    resetForm();

    // Force refresh to ensure we see the updated data
    if (typeof setTimeout !== 'undefined') {
      setTimeout(() => {
        // Clear all loan caches
        clearAllLoanCaches();

        refetch();
      }, 1000);
    }

    return true;
  });

  console.log('[PATCH] Successfully patched loanEditFunctionPatch');
} catch (error) {
  console.error('[PATCH] Failed to patch loanEditFunctionPatch:', error);
}

// Add a global function to manually clear caches
if (typeof window !== 'undefined') {
  window.clearLoanCaches = clearAllLoanCaches;
}

// Export the cache clearing function
export { clearAllLoanCaches };

// Initialize cache clearing on load
clearAllCachesOnLoad();

console.log('[PATCH] Date normalization disabler patch applied successfully');
