/**
 * Date fixer utility to bypass any automatic date normalization
 * This module will directly patch the loan loading/saving process
 */

// Override the parseDate function to handle any date format without normalization
export function forceNormalizeDate(date: string | Date | null | undefined): string {
  if (!date) return '';
  
  // Log the raw date for debugging
  console.log('Raw date:', date);
  
  // If it's already a string, validate format
  if (typeof date === 'string') {
    // If it's already a YYYY-MM-DD format, keep it as is
    if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
      console.log('Using date directly:', date);
      return date;
    }
    
    // Convert to Date and back to string
    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return '';
      
      const result = dateObj.toISOString().split('T')[0];
      console.log('Converted date:', result);
      return result;
    } catch (e) {
      console.error('Error parsing date string:', date, e);
      return '';
    }
  }
  
  // If it's a Date object already
  if (date instanceof Date) {
    try {
      const result = date.toISOString().split('T')[0];
      console.log('Formatted date from Date object:', result);
      return result;
    } catch (e) {
      console.error('Error formatting Date object:', date, e);
      return '';
    }
  }
  
  return '';
}

// Special handler for loan dates specifically
export function fixLoanDate(dateString: string): string {
  // If the date is in 2025, try to make it more realistic
  if (dateString.startsWith('2025-')) {
    // Replace with a date in 2022
    const parts = dateString.split('-');
    const newDate = `2022-${parts[1]}-${parts[2]}`;
    console.log(`Fixed problematic date: ${dateString} -> ${newDate}`);
    return newDate;
  }
  
  return dateString;
}

// Special function to force a loan date update regardless of the current date
export function forceLoanDateUpdate(loanId: string, newDate: string): void {
  try {
    // Get the loan from localStorage first
    const loanKey = `loans/${loanId}`;
    const loanData = window.localStorage.getItem(loanKey);
    
    if (loanData) {
      // Parse the data
      const loan = JSON.parse(loanData);
      
      // Update the date
      loan.next_payment_date = newDate;
      
      // Log the change
      console.log(`Force updating loan ${loanId}:`, loan);
      
      // Save it back
      window.localStorage.setItem(loanKey, JSON.stringify(loan));
    }
  } catch (e) {
    console.error('Error force updating loan date:', e);
  }
}
