import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from './components/Layout';
import { Dashboard } from './pages/Dashboard';
import { Budgets } from './pages/Budgets';
import { Expenses } from './pages/Expenses';
import { Loans } from './pages/Loans';
import { CDs } from './pages/CDs';
import { BankAccounts } from './pages/BankAccounts';
import { Reports } from './pages/Reports';
import { Goals } from './pages/Goals';
import { Calendar } from './pages/Calendar';
import { CalendarTools } from './pages/CalendarTools';
import { FinancialCalendar } from './pages/FinancialCalendar';
import { NetWorth } from './pages/NetWorth';
import { Settings } from './pages/Settings';
import { LoanCoverageFund } from './pages/LoanCoverageFund';
import { DebtPayoffSimulator } from './pages/DebtPayoffSimulator';
import { FinancialTimeline } from './pages/FinancialTimeline';
import { TimelineProjections } from './pages/TimelineProjections';

import { NetWorthProjection } from './pages/NetWorthProjection';
import { ProtectedRoute } from './components/ProtectedRoute';
import { useThemeStore } from './store/themeStore';

// Use only the local auth store - authentication is bypassed
import { useAuthStore } from './store/localAuthStore';

function App() {
  const isDarkMode = useThemeStore((state) => state.isDarkMode);
  const loading = useAuthStore((state) => state.loading);
  const user = useAuthStore((state) => state.user);
  const autoLogin = useAuthStore((state) => state.autoLogin);

  // Apply theme
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [isDarkMode]);

  // Ensure we have a user automatically
  useEffect(() => {
    if (!user) {
      console.log('App: No user found, auto-logging in');
      autoLogin();
    }
  }, [user, autoLogin]);

  // Log application state
  useEffect(() => {
    console.log('App loading state:', loading);
    console.log('Current user:', user?.email || 'None');
    console.log('Auto-login is enabled - authentication bypassed');
  }, [loading, user]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-dark-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-300">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <Routes>
        {/* Redirect login/register to dashboard */}
        <Route path="/login" element={<Navigate to="/" replace />} />
        <Route path="/register" element={<Navigate to="/" replace />} />

        {/* Main app routes */}
        <Route element={<ProtectedRoute />}>
          <Route element={<Layout />}>
            <Route index element={<Dashboard />} />
            <Route path="budgets" element={<Budgets />} />
            <Route path="expenses" element={<Expenses />} />
            <Route path="loans" element={<Loans />} />
            <Route path="cds" element={<CDs />} />
            <Route path="bank-accounts" element={<BankAccounts />} />
            <Route path="reports" element={<Reports />} />
            <Route path="goals" element={<Goals />} />
            <Route path="calendar" element={<Calendar />} />
            <Route path="calendar-tools" element={<CalendarTools />} />
            <Route path="financial-calendar" element={<FinancialCalendar />} />
            <Route path="net-worth" element={<NetWorth />} />
            <Route path="settings" element={<Settings />} />
            <Route path="loan-coverage-fund" element={<LoanCoverageFund />} />
            <Route path="debt-payoff-simulator" element={<DebtPayoffSimulator />} />
            <Route path="financial-timeline" element={<FinancialTimeline />} />
            <Route path="timeline-projections" element={<TimelineProjections />} />

            <Route path="net-worth-projection" element={<NetWorthProjection />} />
          </Route>
        </Route>
      </Routes>
    </Router>
  );
}

export default App;