import React, { useState } from 'react';
import { PlusCircle, Search, Pencil, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import { Modal } from '../components/Modal';
import { ConfirmDialog } from '../components/ConfirmDialog';
import { useAuthStore } from '../store/localAuthStore';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import type { Expense } from '../types/expenses';
import { formatCurrency } from '../utils/currency';
import { localDataStore } from '../lib/local-data-store';

export function Expenses() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedExpense, setSelectedExpense] = useState<Expense | null>(null);
  const [amount, setAmount] = useState('');
  const [category, setCategory] = useState('');
  const [description, setDescription] = useState('');
  const [date, setDate] = useState(format(new Date(), 'yyyy-MM-dd'));
  const user = useAuthStore((state) => state.user);

  // Use the query hook for fetching and add refetch capability
  const { data: expenses, loading, refetch } = useSupabaseQuery<Expense>('expenses', {
    orderBy: { column: 'date', ascending: false }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (selectedExpense) {
        await localDataStore.data.updateItem('expenses', selectedExpense.id, {
          ...selectedExpense,
          amount: Number(amount),
          category,
          description,
          date: new Date(date).toISOString()
        });
      } else {
        const newExpense: Expense = {
          id: localDataStore.utils.generateId(),
          user_id: user?.id || 'default-user',
          amount: Number(amount),
          category,
          description,
          date: new Date(date).toISOString(),
          created_at: new Date().toISOString()
        };
        await localDataStore.data.addItem('expenses', newExpense);
      }

      setIsModalOpen(false);
      resetForm();
      refetch();
    } catch (error) {
      console.error('Error saving expense:', error);
    }
  };

  const handleEdit = (expense: Expense) => {
    setSelectedExpense(expense);
    setAmount(expense.amount.toString());
    setCategory(expense.category);
    setDescription(expense.description || '');
    setDate(format(new Date(expense.date), 'yyyy-MM-dd'));
    setIsModalOpen(true);
  };

  const handleDelete = (expense: Expense) => {
    setSelectedExpense(expense);
    setIsDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (selectedExpense) {
      try {
        await localDataStore.data.removeItem('expenses', selectedExpense.id);
        setIsDeleteModalOpen(false);
        refetch();
      } catch (error) {
        console.error('Error deleting expense:', error);
      }
    }
  };

  const resetForm = () => {
    setSelectedExpense(null);
    setAmount('');
    setCategory('');
    setDescription('');
    setDate(format(new Date(), 'yyyy-MM-dd'));
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  // Calculate expense statistics
  const totalExpenses = expenses?.reduce((sum, expense) => sum + expense.amount, 0) || 0;
  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();
  const monthlyExpenses = expenses?.filter(expense => {
    const expenseDate = new Date(expense.date);
    return expenseDate.getMonth() === currentMonth && expenseDate.getFullYear() === currentYear;
  }).reduce((sum, expense) => sum + expense.amount, 0) || 0;

  const categoryTotals = expenses?.reduce((acc, expense) => {
    acc[expense.category] = (acc[expense.category] || 0) + expense.amount;
    return acc;
  }, {} as Record<string, number>) || {};

  const topCategory = Object.entries(categoryTotals).sort(([,a], [,b]) => b - a)[0];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Expense Tracking</h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Monitor and categorize your spending patterns
          </p>
        </div>
        <button
          onClick={() => {
            resetForm();
            setIsModalOpen(true);
          }}
          className="flex items-center px-4 py-2 bg-rose-600 dark:bg-rose-700 text-white rounded-lg hover:bg-rose-700 dark:hover:bg-rose-600 transition-colors shadow-sm"
        >
          <PlusCircle className="w-5 h-5 mr-2" />
          Add Expense
        </button>
      </div>

      {/* Expense Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-br from-rose-500 to-rose-600 dark:from-rose-600 dark:to-rose-700 p-6 rounded-lg shadow-sm text-white">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-rose-100 font-medium text-sm">Total Expenses</h3>
            <div className="p-2 bg-rose-400/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-rose-100" />
            </div>
          </div>
          <p className="text-3xl font-bold mb-1">{formatCurrency(totalExpenses)}</p>
          <p className="text-rose-200 text-sm">All time spending</p>
        </div>

        <div className="bg-gradient-to-br from-orange-500 to-orange-600 dark:from-orange-600 dark:to-orange-700 p-6 rounded-lg shadow-sm text-white">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-orange-100 font-medium text-sm">This Month</h3>
            <div className="p-2 bg-orange-400/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-orange-100" />
            </div>
          </div>
          <p className="text-3xl font-bold mb-1">{formatCurrency(monthlyExpenses)}</p>
          <p className="text-orange-200 text-sm">{format(new Date(), 'MMMM yyyy')}</p>
        </div>

        <div className="bg-gradient-to-br from-purple-500 to-purple-600 dark:from-purple-600 dark:to-purple-700 p-6 rounded-lg shadow-sm text-white">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-purple-100 font-medium text-sm">Top Category</h3>
            <div className="p-2 bg-purple-400/20 rounded-lg">
              <PlusCircle className="h-4 w-4 text-purple-100" />
            </div>
          </div>
          <p className="text-3xl font-bold mb-1">{topCategory ? formatCurrency(topCategory[1]) : '$0.00'}</p>
          <p className="text-purple-200 text-sm capitalize">{topCategory ? topCategory[0] : 'No expenses yet'}</p>
        </div>
      </div>

      <Modal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          resetForm();
        }}
        title={selectedExpense ? "Edit Expense" : "Add New Expense"}
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Amount
              </label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">$</span>
                <input
                  type="number"
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  min="0"
                  step="0.01"
                  className="block w-full pl-8 rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-rose-500 focus:ring-rose-500 dark:focus:border-rose-400 dark:focus:ring-rose-400"
                  placeholder="0.00"
                  required
                />
              </div>
            </div>

            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category
              </label>
              <select
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-rose-500 focus:ring-rose-500 dark:focus:border-rose-400 dark:focus:ring-rose-400"
                required
              >
                <option value="">Select a category</option>
                <option value="groceries">🛒 Groceries</option>
                <option value="transportation">🚗 Transportation</option>
                <option value="entertainment">🎬 Entertainment</option>
                <option value="utilities">⚡ Utilities</option>
                <option value="food">🍕 Food & Dining</option>
                <option value="healthcare">🏥 Healthcare</option>
                <option value="shopping">🛍️ Shopping</option>
                <option value="other">📝 Other</option>
              </select>
            </div>
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <input
              type="text"
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-rose-500 focus:ring-rose-500 dark:focus:border-rose-400 dark:focus:ring-rose-400"
              placeholder="What was this expense for?"
            />
          </div>

          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Date
            </label>
            <input
              type="date"
              id="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-rose-500 focus:ring-rose-500 dark:focus:border-rose-400 dark:focus:ring-rose-400"
              required
            />
          </div>

          <div className="flex justify-end gap-3 pt-6 border-t border-gray-200 dark:border-gray-600">
            <button
              type="button"
              onClick={() => {
                setIsModalOpen(false);
                resetForm();
              }}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-rose-600 dark:bg-rose-700 text-white rounded-lg hover:bg-rose-700 dark:hover:bg-rose-600 transition-colors shadow-sm"
            >
              {selectedExpense ? "Save Changes" : "Add Expense"}
            </button>
          </div>
        </form>
      </Modal>

      <ConfirmDialog
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={confirmDelete}
        title="Delete Expense"
        message="Are you sure you want to delete this expense? This action cannot be undone."
      />

      {/* Expense List */}
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-6 border-b border-gray-200 dark:border-gray-600">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">💸 Recent Expenses</h3>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search expenses..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-rose-500 dark:focus:ring-rose-400"
              />
            </div>
            <select className="px-4 py-2 border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-rose-500 dark:focus:ring-rose-400">
              <option value="">All Categories</option>
              <option value="groceries">🛒 Groceries</option>
              <option value="transportation">🚗 Transportation</option>
              <option value="entertainment">🎬 Entertainment</option>
              <option value="utilities">⚡ Utilities</option>
              <option value="food">🍕 Food & Dining</option>
              <option value="healthcare">🏥 Healthcare</option>
              <option value="shopping">🛍️ Shopping</option>
              <option value="other">📝 Other</option>
            </select>
          </div>
        </div>

        <div className="overflow-x-auto">
          {expenses && expenses.length > 0 ? (
            <table className="w-full">
              <thead>
                <tr className="bg-gray-50 dark:bg-gray-800">
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Category</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-700">
                {expenses.map((expense) => {
                  const getCategoryIcon = (category: string) => {
                    switch (category.toLowerCase()) {
                      case 'groceries': return '🛒';
                      case 'transportation': return '🚗';
                      case 'entertainment': return '🎬';
                      case 'utilities': return '⚡';
                      case 'food': return '🍕';
                      case 'healthcare': return '🏥';
                      case 'shopping': return '🛍️';
                      default: return '📝';
                    }
                  };

                  return (
                    <tr key={expense.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        {format(new Date(expense.date), 'MMM d, yyyy')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                        <div className="flex items-center gap-2">
                          <span>{getCategoryIcon(expense.category)}</span>
                          <span className="capitalize">{expense.category}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {expense.description || 'No description'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-rose-600 dark:text-rose-400 text-right">
                        {formatCurrency(expense.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                        <div className="flex justify-end gap-1">
                          <button
                            onClick={() => handleEdit(expense)}
                            className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                          >
                            <Pencil className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(expense)}
                            className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                <PlusCircle className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No expenses yet</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-6">Start tracking your spending by adding your first expense</p>
              <button
                onClick={() => {
                  resetForm();
                  setIsModalOpen(true);
                }}
                className="inline-flex items-center px-4 py-2 bg-rose-600 dark:bg-rose-700 text-white rounded-lg hover:bg-rose-700 dark:hover:bg-rose-600 transition-colors"
              >
                <PlusCircle className="w-5 h-5 mr-2" />
                Add Your First Expense
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}