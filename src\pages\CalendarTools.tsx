import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { localDataStore } from '../lib/local-data-store';
import { EventTemplates } from '../components/EventTemplates';
import { FinancialAnalytics } from '../components/FinancialAnalytics';
import { ChevronLeft } from 'lucide-react';

// Define interfaces for our data types
interface Loan {
  id: string;
  user_id: string;
  name: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  remaining_balance: number;
  monthly_payment: number;
  next_payment_date: string;
  end_date?: string;
  created_at?: string;
}

interface CD {
  id: string;
  user_id: string;
  institution: string;
  principal: number;
  interest_rate: number;
  term_months: number;
  maturity_date: string;
}

interface FinancialGoal {
  id: string;
  user_id: string;
  name: string;
  target_amount: number;
  target_date: string;
  notes: string;
}

interface Expense {
  id: string;
  user_id: string;
  amount: number;
  category: string;
  description: string;
  date: string;
}

interface CustomEvent {
  id: string;
  user_id: string;
  title: string;
  start_date: string;
  end_date?: string;
  type: string;
  amount?: number;
  description?: string;
  created_at: string;
  backgroundColor?: string;
  borderColor?: string;
  textColor?: string;
  isRecurring?: boolean;
  recurrencePattern?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  recurrenceEndDate?: string;
  recurrenceCount?: number;
  reminder?: boolean;
  reminderTime?: number;
  reminderUnit?: 'minutes' | 'hours' | 'days';
  reminderSent?: boolean;
  category?: string;
  templateId?: string;
}

export function CalendarTools() {
  // State for data
  const [loans, setLoans] = useState<Loan[]>([]);
  const [cds, setCds] = useState<CD[]>([]);
  const [goals, setGoals] = useState<FinancialGoal[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [customEvents, setCustomEvents] = useState<CustomEvent[]>([]);

  // UI state
  const [activeTab, setActiveTab] = useState<'templates' | 'analytics'>('templates');

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  // Load data from local storage
  const loadData = async () => {
    try {
      // Load loans
      const loansData = await localDataStore.data.loadCollection('loans') || [];
      setLoans(loansData as Loan[]);

      // Load CDs
      const cdsData = await localDataStore.data.loadCollection('cds') || [];
      setCds(cdsData as CD[]);

      // Load goals
      const goalsData = await localDataStore.data.loadCollection('financial_goals') || [];
      setGoals(goalsData as FinancialGoal[]);

      // Load expenses
      const expensesData = await localDataStore.data.loadCollection('expenses') || [];
      setExpenses(expensesData as Expense[]);

      // Load custom events
      const customEventsData = await localDataStore.data.loadCollection('calendar_events') || [];
      setCustomEvents(customEventsData as CustomEvent[]);

      console.log('Data loaded successfully for Calendar Tools');
    } catch (error) {
      console.error('Error loading data:', error);
    }
  };

  // Show toast notification
  const showToast = (message: string) => {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded shadow-lg z-50';
    toast.textContent = message;
    document.body.appendChild(toast);

    // Remove toast after 3 seconds
    setTimeout(() => {
      toast.classList.add('animate-fade-out');
      setTimeout(() => {
        document.body.removeChild(toast);
      }, 300);
    }, 3000);
  };

  // Handle using a template
  const handleUseTemplate = (template: any) => {
    // Store the selected template in localStorage to be used by the Calendar page
    localStorage.setItem('selectedTemplate', JSON.stringify(template));
    // Navigate to the Calendar page
    window.location.href = '/#/calendar';
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Calendar Tools</h2>
        <Link
          to="/calendar"
          className="flex items-center text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-300"
        >
          <ChevronLeft className="w-4 h-4 mr-1" /> Back to Calendar
        </Link>
      </div>

      {/* Tab navigation */}
      <div className="mb-6 border-b border-gray-200 dark:border-dark-600">
        <ul className="flex flex-wrap -mb-px">
          <li className="mr-2">
            <button
              onClick={() => setActiveTab('templates')}
              className={`inline-block py-2 px-4 text-sm font-medium ${activeTab === 'templates' ? 'text-indigo-600 border-b-2 border-indigo-600 dark:text-indigo-400 dark:border-indigo-400' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
            >
              Event Templates
            </button>
          </li>
          <li className="mr-2">
            <button
              onClick={() => setActiveTab('analytics')}
              className={`inline-block py-2 px-4 text-sm font-medium ${activeTab === 'analytics' ? 'text-indigo-600 border-b-2 border-indigo-600 dark:text-indigo-400 dark:border-indigo-400' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
            >
              Financial Analytics
            </button>
          </li>
        </ul>
      </div>

      {/* Tab content */}
      <div className="bg-white dark:bg-dark-800 rounded-lg shadow-md p-6">
        {activeTab === 'templates' ? (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Event Templates</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Create and manage templates for recurring financial events. Templates make it easy to add common events to your calendar with consistent details.
            </p>
            <EventTemplates
              onUseTemplate={handleUseTemplate}
              showToast={showToast}
            />
          </div>
        ) : (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Financial Analytics</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              View financial analytics based on your calendar events. See cash flow projections, upcoming obligations, and event summaries by category.
            </p>
            <FinancialAnalytics
              loans={loans}
              cds={cds}
              goals={goals}
              expenses={expenses}
              customEvents={customEvents}
            />
          </div>
        )}
      </div>
    </div>
  );
}
