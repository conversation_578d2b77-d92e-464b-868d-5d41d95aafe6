/*
  # Create stocks table and related schemas

  1. New Tables
    - `stocks`
      - `id` (uuid, primary key)
      - `symbol` (text, unique)
      - `name` (text)
      - `price` (numeric)
      - `change` (numeric)
      - `volume` (numeric)
      - `market_cap` (numeric)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)
    - `stock_data`
      - `id` (uuid, primary key)
      - `stock_id` (uuid, foreign key)
      - `date` (date)
      - `open` (numeric)
      - `high` (numeric)
      - `low` (numeric)
      - `close` (numeric)
      - `volume` (numeric)

  2. Security
    - Enable RLS on both tables
    - Add policies for authenticated users
*/

-- Create stocks table
CREATE TABLE IF NOT EXISTS stocks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  symbol text UNIQUE NOT NULL,
  name text NOT NULL,
  price numeric NOT NULL,
  change numeric NOT NULL,
  volume numeric NOT NULL,
  market_cap numeric NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create stock_data table
CREATE TABLE IF NOT EXISTS stock_data (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  stock_id uuid REFERENCES stocks(id) ON DELETE CASCADE,
  date date NOT NULL,
  open numeric NOT NULL,
  high numeric NOT NULL,
  low numeric NOT NULL,
  close numeric NOT NULL,
  volume numeric NOT NULL,
  UNIQUE(stock_id, date)
);

-- Enable RLS
ALTER TABLE stocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE stock_data ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Allow authenticated users to read stocks"
  ON stocks
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to read stock data"
  ON stock_data
  FOR SELECT
  TO authenticated
  USING (true);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_stocks_updated_at
  BEFORE UPDATE ON stocks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at();