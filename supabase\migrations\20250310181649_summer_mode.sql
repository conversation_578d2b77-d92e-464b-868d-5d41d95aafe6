/*
  # Initial Schema Setup for FinanceTrack Application

  1. Tables
    - users (managed by <PERSON><PERSON><PERSON> Auth)
    - budgets
    - expenses
    - loans
    - cds (Certificates of Deposit)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users
*/

-- Budgets table
CREATE TABLE IF NOT EXISTS budgets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  category text NOT NULL,
  amount decimal NOT NULL,
  spent decimal DEFAULT 0,
  period text CHECK (period IN ('monthly', 'yearly')) NOT NULL,
  created_at timestamptz DEFAULT now(),
  CONSTRAINT positive_amount CHECK (amount >= 0),
  CONSTRAINT positive_spent CHECK (spent >= 0)
);

ALTER TABLE budgets ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own budgets"
  ON budgets
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Expenses table
CREATE TABLE IF NOT EXISTS expenses (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  amount decimal NOT NULL,
  category text NOT NULL,
  description text,
  date timestamptz NOT NULL,
  created_at timestamptz DEFAULT now(),
  CONSTRAINT positive_amount CHECK (amount >= 0)
);

ALTER TABLE expenses ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own expenses"
  ON expenses
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- Loans table
CREATE TABLE IF NOT EXISTS loans (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  principal decimal NOT NULL,
  interest_rate decimal NOT NULL,
  term_months integer NOT NULL,
  remaining_balance decimal NOT NULL,
  monthly_payment decimal NOT NULL,
  next_payment_date date NOT NULL,
  created_at timestamptz DEFAULT now(),
  CONSTRAINT positive_principal CHECK (principal >= 0),
  CONSTRAINT positive_interest CHECK (interest_rate >= 0),
  CONSTRAINT positive_term CHECK (term_months > 0),
  CONSTRAINT positive_balance CHECK (remaining_balance >= 0),
  CONSTRAINT positive_payment CHECK (monthly_payment >= 0)
);

ALTER TABLE loans ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own loans"
  ON loans
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);

-- CDs table
CREATE TABLE IF NOT EXISTS cds (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  institution text NOT NULL,
  principal decimal NOT NULL,
  interest_rate decimal NOT NULL,
  term_months integer NOT NULL,
  maturity_date date NOT NULL,
  created_at timestamptz DEFAULT now(),
  CONSTRAINT positive_principal CHECK (principal >= 0),
  CONSTRAINT positive_interest CHECK (interest_rate >= 0),
  CONSTRAINT positive_term CHECK (term_months > 0)
);

ALTER TABLE cds ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can manage their own CDs"
  ON cds
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id);