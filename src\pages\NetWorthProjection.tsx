import { useState, useEffect } from 'react';
import { useSupabaseQuery } from '../hooks/useSupabaseQuery';
import { Loan, CD, BankAccount } from '../types/index';
import { useAuthStore } from '../store/localAuthStore';
import { formatCurrency } from '../utils/currency';
import { addMonths, format, parseISO } from 'date-fns';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface NetWorthDataPoint {
  date: Date;
  monthLabel: string;
  totalAssets: number;
  totalLiabilities: number;
  netWorth: number;
}

export function NetWorthProjection() {
  const { user } = useAuthStore();
  const [projectionMonths, setProjectionMonths] = useState<number>(60);
  const [growthRate, setGrowthRate] = useState<number>(5); // annual growth rate in %
  const [netWorthData, setNetWorthData] = useState<NetWorthDataPoint[]>([]);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  // Get all loans
  const { data: loans, loading: loansLoading } = useSupabaseQuery<Loan>('loans', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Get all CDs
  const { data: cds, loading: cdsLoading } = useSupabaseQuery<CD>('cds', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Get all bank accounts
  const { data: bankAccounts, loading: bankAccountsLoading } = useSupabaseQuery<BankAccount>('bank_accounts', {
    filters: user?.id ? [{ column: 'user_id', value: user.id }] : []
  });

  // Calculate projections when data changes
  useEffect(() => {
    if (!loansLoading && !cdsLoading && !bankAccountsLoading) {
      generateNetWorthProjection();
    }
  }, [loans, cds, bankAccounts, projectionMonths, growthRate]);

  // Generate net worth projection data
  const generateNetWorthProjection = () => {
    setIsGenerating(true);

    const today = new Date();
    const projectedData: NetWorthDataPoint[] = [];

    // Clone loans to work with
    const loansCopy = loans ? JSON.parse(JSON.stringify(loans)) : [];

    // Clone CDs to work with
    const cdsCopy = cds ? JSON.parse(JSON.stringify(cds)) : [];

    // Start with current values
    let currentAssets = calculateCurrentAssets();
    let currentLiabilities = calculateCurrentLiabilities(loansCopy);

    // Calculate monthly growth rate (compounded)
    const monthlyGrowthRate = Math.pow(1 + (growthRate / 100), 1/12) - 1;

    // Generate projection for each month
    for (let i = 0; i < projectionMonths; i++) {
      const currentDate = addMonths(today, i);
      const monthLabel = format(currentDate, 'MMM yyyy');

      // Update loan balances for this month (reduce principal)
      loansCopy.forEach((loan: Loan) => {
        if (loan.remaining_balance <= 0) return;

        // Calculate interest for this month
        const monthlyRate = (loan.interest_rate / 100) / 12;
        const interestPayment = loan.remaining_balance * monthlyRate;

        // Calculate principal payment
        const principalPayment = Math.min(loan.monthly_payment - interestPayment, loan.remaining_balance);

        // Update loan balance
        loan.remaining_balance -= principalPayment;
      });

      // Check for CD maturities in this month and add to assets
      cdsCopy.forEach((cd: CD) => {
        const maturityDate = parseISO(cd.maturity_date);

        if (format(currentDate, 'yyyy-MM') === format(maturityDate, 'yyyy-MM')) {
          // CD matured this month, add principal plus interest to assets
          const monthsHeld = cd.term_months;
          const interestRate = cd.interest_rate / 100;
          // Simple interest calculation
          const interest = cd.principal * interestRate * (monthsHeld / 12);
          currentAssets += cd.principal + interest;

          // Remove CD from tracking (set to 0)
          cd.principal = 0;
        }
      });

      // Apply growth to assets (excluding CDs which have their own interest)
      const cdTotal = cdsCopy.reduce((sum: number, cd: CD) => sum + cd.principal, 0);
      const nonCdAssets = currentAssets - cdTotal;

      // Apply monthly growth
      currentAssets = nonCdAssets * (1 + monthlyGrowthRate) + cdTotal;

      // Recalculate liabilities
      currentLiabilities = calculateCurrentLiabilities(loansCopy);

      // Calculate net worth
      const netWorth = currentAssets - currentLiabilities;

      // Add data point
      projectedData.push({
        date: currentDate,
        monthLabel,
        totalAssets: currentAssets,
        totalLiabilities: currentLiabilities,
        netWorth
      });
    }

    setNetWorthData(projectedData);
    setIsGenerating(false);
  };

  // Calculate current total assets
  const calculateCurrentAssets = (): number => {
    let total = 0;

    // Add bank account balances
    if (bankAccounts) {
      total += bankAccounts.reduce((sum: number, account: BankAccount) => sum + account.balance, 0);
    }

    // Add CD principals
    if (cds) {
      total += cds.reduce((sum: number, cd: CD) => sum + cd.principal, 0);
    }

    return total;
  };

  // Calculate current total liabilities
  const calculateCurrentLiabilities = (loansCopy: Loan[]): number => {
    if (!loansCopy) return 0;
    return loansCopy.reduce((sum: number, loan: Loan) => sum + Math.max(0, loan.remaining_balance), 0);
  };

  // Prepare chart data
  const getChartData = () => {
    return {
      labels: netWorthData.map(d => d.monthLabel),
      datasets: [
        {
          label: 'Net Worth',
          data: netWorthData.map(d => d.netWorth),
          borderColor: 'rgb(16, 185, 129)', // green
          backgroundColor: 'rgba(16, 185, 129, 0.1)',
          tension: 0.4,
          fill: true
        },
        {
          label: 'Assets',
          data: netWorthData.map(d => d.totalAssets),
          borderColor: 'rgb(59, 130, 246)', // blue
          backgroundColor: 'transparent',
          borderDash: [5, 5],
          tension: 0.4
        },
        {
          label: 'Liabilities',
          data: netWorthData.map(d => d.totalLiabilities),
          borderColor: 'rgb(239, 68, 68)', // red
          backgroundColor: 'transparent',
          borderDash: [5, 5],
          tension: 0.4
        }
      ]
    };
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Net Worth Projection',
        color: 'rgb(107, 114, 128)'
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              label += formatCurrency(context.parsed.y);
            }
            return label;
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Month',
          color: 'rgb(107, 114, 128)'
        },
        ticks: {
          maxTicksLimit: 12
        }
      },
      y: {
        title: {
          display: true,
          text: 'Amount',
          color: 'rgb(107, 114, 128)'
        },
        ticks: {
          callback: function(value: any) {
            return formatCurrency(value);
          }
        }
      }
    }
  };

  // Loading state
  if (loansLoading || cdsLoading || bankAccountsLoading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Net Worth Projection</h1>
      </div>

      <div className="bg-white dark:bg-dark-800 p-6 rounded-lg shadow space-y-6">
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Settings</h2>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Projection Months
              </label>
              <select
                value={projectionMonths}
                onChange={(e) => setProjectionMonths(Number(e.target.value))}
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white"
              >
                <option value={12}>1 year</option>
                <option value={24}>2 years</option>
                <option value={36}>3 years</option>
                <option value={60}>5 years</option>
                <option value={120}>10 years</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Annual Growth Rate (%)
              </label>
              <input
                type="number"
                value={growthRate}
                onChange={(e) => setGrowthRate(Number(e.target.value))}
                min="0"
                max="20"
                step="0.5"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-dark-700 dark:border-dark-500 dark:text-white"
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Expected annual growth rate for your investments and savings
              </p>
            </div>
          </div>

          <div className="mt-4">
            <button
              onClick={generateNetWorthProjection}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              disabled={isGenerating}
            >
              {isGenerating ? 'Generating...' : 'Regenerate Projection'}
            </button>
          </div>
        </div>

        {/* Current Net Worth Summary */}
        <div className="bg-gray-50 dark:bg-dark-700 p-4 rounded-lg">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Current Assets</h3>
              <p className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                {formatCurrency(calculateCurrentAssets())}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Current Liabilities</h3>
              <p className="mt-1 text-lg font-semibold text-gray-900 dark:text-white">
                {formatCurrency(calculateCurrentLiabilities(loans as Loan[]))}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Current Net Worth</h3>
              <p className="mt-1 text-lg font-semibold text-indigo-600 dark:text-indigo-400">
                {formatCurrency(calculateCurrentAssets() - calculateCurrentLiabilities(loans as Loan[]))}
              </p>
            </div>
          </div>
        </div>

        {/* Net Worth Projection Chart */}
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Net Worth Projection Chart</h2>
          <div className="h-80">
            {netWorthData.length > 0 ? (
              <Line data={getChartData()} options={chartOptions} />
            ) : (
              <div className="flex justify-center items-center h-full">
                <p className="text-gray-500 dark:text-gray-400">No projection data available. Update your settings and generate the projection.</p>
              </div>
            )}
          </div>
        </div>

        {/* Net Worth Projection Table */}
        <div>
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Projection Details</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-dark-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Assets</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Liabilities</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Net Worth</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-dark-800 divide-y divide-gray-200 dark:divide-gray-700">
                {netWorthData.filter((_, index) => index % 6 === 0 || index === netWorthData.length - 1).map((entry, index) => (
                  <tr key={index} className="hover:bg-gray-50 dark:hover:bg-dark-700">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{entry.monthLabel}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-blue-600 dark:text-blue-400">{formatCurrency(entry.totalAssets)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-right text-red-600 dark:text-red-400">{formatCurrency(entry.totalLiabilities)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-right text-green-600 dark:text-green-400">{formatCurrency(entry.netWorth)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 text-center">
            *Showing values at 6-month intervals for readability
          </p>
        </div>
      </div>
    </div>
  );
}
