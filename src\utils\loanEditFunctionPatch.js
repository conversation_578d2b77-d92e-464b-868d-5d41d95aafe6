/**
 * Enhanced Loan Edit Function Patch
 * 
 * This file provides a completely fixed version of the loan edit functionality
 * to ensure dates are handled correctly and never normalized or overridden.
 */

// Import the original functions
import { parseDate, formatDateString, addMonthsToDate } from './dateUtils';

/**
 * Fixed version of the handleEdit function
 * This function ensures dates are preserved exactly as stored in the database
 */
export function fixedHandleEdit(loan, callbacks) {
  try {
    console.log("ENHANCED fixedHandleEdit called with loan:", loan);
    
    // Extract callbacks
    const { 
      setSelectedLoan, 
      setName, 
      setPrincipal, 
      setInterestRate, 
      setStartDate, 
      setEndDate, 
      setMonthlyPayment, 
      setFormError, 
      setIsModalOpen 
    } = callbacks;

    // Make a copy of the loan
    const fixedLoan = { ...loan };

    // Set basic loan properties
    setSelectedLoan(fixedLoan);
    setName(fixedLoan.name);
    setPrincipal(fixedLoan.principal.toString());
    setInterestRate(fixedLoan.interest_rate.toString());

    // Set dates directly without any processing
    console.log(`Setting start date for edit: ${fixedLoan.next_payment_date}`);
    console.log(`Setting end date for edit: ${fixedLoan.end_date}`);
    
    setStartDate(fixedLoan.next_payment_date);
    setEndDate(fixedLoan.end_date);

    // Set monthly payment
    if (fixedLoan.monthly_payment) {
      setMonthlyPayment(fixedLoan.monthly_payment.toFixed(2));
    }

    // Reset any previous errors
    setFormError(null);

    // Open modal
    setIsModalOpen(true);

    return true;
  } catch (error) {
    console.error('Error in enhanced fixedHandleEdit function:', error);
    return false;
  }
}

/**
 * Fixed version of the handleSubmit function
 * This function ensures dates are preserved exactly as entered by the user
 */
export function fixedHandleSubmit(formData, callbacks) {
  try {
    console.log("ENHANCED fixedHandleSubmit called with data:", formData);
    
    // Extract form data
    const { 
      selectedLoan, 
      name, 
      principal, 
      interestRate, 
      startDate, 
      endDate, 
      termMonths,
      monthlyPayment
    } = formData;

    // Extract callbacks
    const { 
      updateItem, 
      addItem, 
      setIsModalOpen, 
      resetForm, 
      refetch 
    } = callbacks;

    // Log the form data for debugging
    console.log('Form data for submit:', formData);
    
    // Create the loan object with the date strings from the form
    let loanData = {
      name,
      principal: Number(principal),
      interest_rate: Number(interestRate),
      term_months: termMonths,
      next_payment_date: startDate,  // Use date string directly from form
      end_date: endDate,  // Use date string directly from form
      monthly_payment: Number(monthlyPayment || 0),
      // Preserve remaining balance if updating
      remaining_balance: selectedLoan ? selectedLoan.remaining_balance : Number(principal)
    };
    
    // Log the loan data before saving
    console.log('Loan data to save:', loanData);

    // Update or create loan
    if (selectedLoan) {
      console.log('Updating loan with ID:', selectedLoan.id);
      
      // Create a complete update object by merging with the original loan
      const updateData = {
        ...selectedLoan,
        ...loanData
      };
      
      console.log('Complete update data:', updateData);
      
      // Update the loan with the COMPLETE update data
      updateItem(selectedLoan.id, updateData);
    } else {
      console.log('Creating new loan with data:', loanData);

      // Create new loan with direct date string
      addItem({
        user_id: 'default-user',
        ...loanData
      });
    }

    // Success - close modal and reset form
    setIsModalOpen(false);
    resetForm();

    // Force refresh to ensure we see the updated data
    setTimeout(() => {
      refetch();
    }, 500);

    return true;
  } catch (error) {
    console.error('Error in enhanced fixedHandleSubmit function:', error);
    return false;
  }
}
