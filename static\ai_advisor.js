// AI Advisor JavaScript Functions

// Chat functionality
function handleChatKeyPress(event) {
    if (event.key === 'Enter') {
        sendChatMessage();
    }
}

function sendChatMessage() {
    const input = document.getElementById('chatInput');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add user message to chat
    addMessageToChat('user', message);
    
    // Clear input and disable send button
    input.value = '';
    const sendButton = document.getElementById('sendButton');
    sendButton.disabled = true;
    sendButton.textContent = 'Thinking...';
    
    // Add loading message
    const loadingId = addMessageToChat('ai', 'Analyzing your question...');
    
    // Send to AI
    fetch('/api/ai_chat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        // Remove loading message
        removeMessage(loadingId);
        
        // Add AI response
        if (data.error) {
            addMessageToChat('ai', `Sorry, I encountered an error: ${data.error}`);
        } else {
            addMessageToChat('ai', data.response);
        }
    })
    .catch(error => {
        removeMessage(loadingId);
        addMessageToChat('ai', 'Sorry, I\'m having trouble connecting right now. Please try again.');
        console.error('Chat error:', error);
    })
    .finally(() => {
        // Re-enable send button
        sendButton.disabled = false;
        sendButton.textContent = 'Send';
    });
}

function addMessageToChat(sender, message) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    const messageId = 'msg_' + Date.now();
    messageDiv.id = messageId;
    
    if (sender === 'user') {
        messageDiv.className = 'message user-message';
        messageDiv.innerHTML = `<strong>You:</strong> ${message}`;
    } else {
        messageDiv.className = 'message ai-message';
        messageDiv.innerHTML = `<strong>AI Advisor:</strong> ${message}`;
    }
    
    chatMessages.appendChild(messageDiv);
    chatMessages.scrollTop = chatMessages.scrollHeight;
    
    return messageId;
}

function removeMessage(messageId) {
    const message = document.getElementById(messageId);
    if (message) {
        message.remove();
    }
}

// Analysis functions
function analyzeExpenses() {
    showLoading('Analyzing your expenses...');
    
    fetch('/api/ai_analyze_expenses', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.error) {
            showError(`Error analyzing expenses: ${data.error}`);
        } else {
            showAnalysisResult('Expense Analysis', data);
        }
    })
    .catch(error => {
        hideLoading();
        showError('Failed to analyze expenses. Please try again.');
        console.error('Expense analysis error:', error);
    });
}

function optimizeLoans() {
    showLoading('Optimizing your loan strategy...');
    
    fetch('/api/ai_optimize_loans', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.error) {
            showError(`Error optimizing loans: ${data.error}`);
        } else {
            showAnalysisResult('Loan Optimization', data);
        }
    })
    .catch(error => {
        hideLoading();
        showError('Failed to optimize loans. Please try again.');
        console.error('Loan optimization error:', error);
    });
}

function financialHealthCheck() {
    showLoading('Performing comprehensive financial health check...');
    
    fetch('/api/ai_financial_health', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.error) {
            showError(`Error checking financial health: ${data.error}`);
        } else {
            showAnalysisResult('Financial Health Check', data);
        }
    })
    .catch(error => {
        hideLoading();
        showError('Failed to check financial health. Please try again.');
        console.error('Financial health check error:', error);
    });
}

// UI helper functions
function showLoading(message) {
    const resultsDiv = document.getElementById('analysisResults');
    resultsDiv.innerHTML = `
        <div class="analysis-result">
            <div class="loading">${message}</div>
        </div>
    `;
}

function hideLoading() {
    // Loading will be replaced by results or error
}

function showError(message) {
    const resultsDiv = document.getElementById('analysisResults');
    resultsDiv.innerHTML = `
        <div class="error-message">
            <strong>Error:</strong> ${message}
        </div>
    `;
}

function showAnalysisResult(title, data) {
    const resultsDiv = document.getElementById('analysisResults');
    
    let content = `
        <div class="analysis-result">
            <h3>📊 ${title}</h3>
            <div style="white-space: pre-wrap; line-height: 1.6;">${data.analysis}</div>
    `;
    
    // Add summary metrics if available
    if (data.total_expenses !== undefined) {
        content += `
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                <h4>📈 Key Metrics</h4>
                <p><strong>Total Monthly Expenses:</strong> $${data.total_expenses.toLocaleString()}</p>
                ${data.monthly_income ? `<p><strong>Monthly Income:</strong> $${data.monthly_income.toLocaleString()}</p>` : ''}
                ${data.savings_rate !== undefined ? `<p><strong>Savings Rate:</strong> ${data.savings_rate.toFixed(1)}%</p>` : ''}
            </div>
        `;
    }
    
    if (data.total_debt !== undefined) {
        content += `
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                <h4>💰 Debt Summary</h4>
                <p><strong>Total Debt:</strong> $${data.total_debt.toLocaleString()}</p>
                <p><strong>Monthly Payments:</strong> $${data.monthly_payments.toLocaleString()}</p>
                <p><strong>Number of Loans:</strong> ${data.loan_count}</p>
            </div>
        `;
    }
    
    if (data.net_worth !== undefined) {
        content += `
            <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 10px;">
                <h4>🏦 Financial Position</h4>
                <p><strong>Net Worth:</strong> $${data.net_worth.toLocaleString()}</p>
                <p><strong>Total Assets:</strong> $${data.total_assets.toLocaleString()}</p>
                <p><strong>Total Debt:</strong> $${data.total_debt.toLocaleString()}</p>
                ${data.debt_to_asset_ratio !== undefined ? `<p><strong>Debt-to-Asset Ratio:</strong> ${data.debt_to_asset_ratio.toFixed(1)}%</p>` : ''}
            </div>
        `;
    }
    
    content += '</div>';
    resultsDiv.innerHTML = content;
    
    // Scroll to results
    resultsDiv.scrollIntoView({ behavior: 'smooth' });
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Advisor page loaded');
    
    // Focus on chat input
    document.getElementById('chatInput').focus();
});
