@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 dark:bg-dark-900 text-gray-900 dark:text-gray-100;
  }

  input, select, textarea {
    @apply bg-white dark:bg-dark-800 text-gray-900 dark:text-white border-gray-300 dark:border-dark-600;
  }

  input:focus, select:focus, textarea:focus {
    @apply ring-indigo-500 dark:ring-indigo-400 border-indigo-500 dark:border-indigo-400;
  }

  button {
    @apply text-gray-700 dark:text-gray-300;
  }

  button.primary {
    @apply bg-indigo-600 dark:bg-indigo-500 text-white hover:bg-indigo-700 dark:hover:bg-indigo-600;
  }

  button.secondary {
    @apply bg-white dark:bg-dark-800 border border-gray-300 dark:border-dark-600 hover:bg-gray-50 dark:hover:bg-dark-700;
  }
}

/* Calendar Dark Mode */
.dark .fc {
  --fc-border-color: theme('colors.dark.700');
  --fc-page-bg-color: theme('colors.dark.800');
  --fc-neutral-bg-color: theme('colors.dark.700');
  --fc-list-event-hover-bg-color: theme('colors.dark.700');
  --fc-today-bg-color: theme('colors.indigo.900' / 0.3);
}

.dark .fc-theme-standard td,
.dark .fc-theme-standard th {
  @apply border-dark-700;
}

.dark .fc-theme-standard .fc-scrollgrid {
  @apply border-dark-700;
}

.dark .fc .fc-button {
  @apply bg-dark-700 border-dark-600 hover:bg-dark-600;
}

.dark .fc .fc-button-primary:not(:disabled).fc-button-active,
.dark .fc .fc-button-primary:not(:disabled):active {
  @apply bg-indigo-600 border-indigo-700;
}

.dark .fc-daygrid-day-number,
.dark .fc-col-header-cell-cushion {
  @apply text-gray-300;
}