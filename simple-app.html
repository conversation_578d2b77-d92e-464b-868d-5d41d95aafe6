<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Advanced Trading Advisor - Simplified</title>
  <!-- Include Tailwind CSS from CDN -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Include React from CDN -->
  <script crossorigin src="https://unpkg.com/react@18.3.1/umd/react.production.min.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18.3.1/umd/react-dom.production.min.js"></script>
  <!-- Include Lightweight Charts -->
  <script src="https://unpkg.com/lightweight-charts@4.1.3/dist/lightweight-charts.standalone.production.js"></script>
  <!-- Include Lucide Icons -->
  <script src="https://unpkg.com/lucide@0.344.0/dist/umd/lucide.min.js"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f9fafb;
    }
    .card {
      background-color: white;
      border-radius: 0.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 1.5rem;
      margin-bottom: 1rem;
    }
    .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.5rem 1rem;
      font-weight: 500;
      border-radius: 0.375rem;
      cursor: pointer;
    }
    .btn-primary {
      background-color: #2563eb;
      color: white;
    }
    .btn-primary:hover {
      background-color: #1d4ed8;
    }
    .chart-container {
      height: 400px;
      width: 100%;
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- App will be loaded here -->
    <div class="min-h-screen p-4">
      <header class="mb-6">
        <h1 class="text-3xl font-bold">Advanced Trading Advisor</h1>
        <p class="text-gray-600">Simplified Local Version</p>
      </header>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Stock Chart -->
        <div class="card md:col-span-2">
          <h2 class="text-xl font-semibold mb-4">Stock Chart</h2>
          <div class="chart-container" id="chart"></div>
        </div>
        
        <!-- Trading Signals -->
        <div class="card">
          <h2 class="text-xl font-semibold mb-4">Trading Signals</h2>
          <div id="signals" class="space-y-3">
            <div class="bg-green-100 p-3 rounded-md">
              <div class="font-medium text-green-800">BUY Signal</div>
              <div class="text-sm text-green-700">Moving average crossover detected</div>
            </div>
            <div class="bg-yellow-100 p-3 rounded-md">
              <div class="font-medium text-yellow-800">HOLD Signal</div>
              <div class="text-sm text-yellow-700">RSI in neutral zone (45)</div>
            </div>
          </div>
        </div>
        
        <!-- Technical Indicators -->
        <div class="card">
          <h2 class="text-xl font-semibold mb-4">Technical Indicators</h2>
          <table class="w-full">
            <tbody>
              <tr>
                <td class="py-2">RSI (14)</td>
                <td class="text-right">45.23</td>
              </tr>
              <tr>
                <td class="py-2">MACD (12,26,9)</td>
                <td class="text-right">1.56</td>
              </tr>
              <tr>
                <td class="py-2">Bollinger Bands</td>
                <td class="text-right">Upper: 156.78<br>Middle: 151.34<br>Lower: 145.90</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- AI Insights -->
        <div class="card">
          <h2 class="text-xl font-semibold mb-4">AI Insights</h2>
          <p class="text-gray-700 mb-3">Based on current market conditions and technical analysis, there is a <span class="font-semibold text-green-600">65%</span> probability of upward movement in the next trading session.</p>
          <div class="bg-blue-50 p-3 rounded-md">
            <div class="font-medium text-blue-800">Key Factors:</div>
            <ul class="text-sm text-blue-700 list-disc pl-5 mt-1">
              <li>Positive market sentiment</li>
              <li>Strong support levels holding</li>
              <li>Increasing trading volume</li>
            </ul>
          </div>
        </div>
        
        <!-- Risk Management -->
        <div class="card">
          <h2 class="text-xl font-semibold mb-4">Risk Management</h2>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span>Recommended Position Size:</span>
              <span class="font-medium">5% of portfolio</span>
            </div>
            <div class="flex justify-between">
              <span>Stop Loss:</span>
              <span class="font-medium text-red-600">$148.50 (-2.5%)</span>
            </div>
            <div class="flex justify-between">
              <span>Take Profit:</span>
              <span class="font-medium text-green-600">$158.75 (+4.8%)</span>
            </div>
            <div class="flex justify-between">
              <span>Risk/Reward Ratio:</span>
              <span class="font-medium">1:1.92</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Initialize chart when the page loads
    document.addEventListener('DOMContentLoaded', function() {
      // Create sample data
      const data = generateSampleData();
      
      // Create chart
      const chartContainer = document.getElementById('chart');
      const chart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: chartContainer.clientHeight,
        layout: {
          background: { color: '#ffffff' },
          textColor: '#333333',
        },
        grid: {
          vertLines: { color: '#f0f3fa' },
          horzLines: { color: '#f0f3fa' },
        },
        crosshair: {
          mode: LightweightCharts.CrosshairMode.Normal,
        },
        timeScale: {
          borderColor: '#d1d5db',
          timeVisible: true,
        },
      });
      
      // Add candlestick series
      const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
      });
      
      candlestickSeries.setData(data);
      
      // Make the chart responsive
      window.addEventListener('resize', () => {
        chart.applyOptions({
          width: chartContainer.clientWidth,
        });
      });
    });
    
    // Generate sample stock price data
    function generateSampleData() {
      const data = [];
      let time = new Date(Date.UTC(2023, 0, 1, 0, 0, 0, 0));
      let basePrice = 150;
      let volatility = 1;
      
      for (let i = 0; i < 100; i++) {
        const open = basePrice + (Math.random() - 0.5) * volatility * 2;
        const high = open + Math.random() * volatility;
        const low = open - Math.random() * volatility;
        const close = (open + high + low) / 3 + (Math.random() - 0.5) * volatility;
        
        data.push({
          time: time.getTime() / 1000,
          open: open,
          high: high,
          low: low,
          close: close,
        });
        
        basePrice = close;
        time.setDate(time.getDate() + 1);
      }
      
      return data;
    }
  </script>
</body>
</html>
