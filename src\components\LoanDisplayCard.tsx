import React from 'react';
import { Loan } from '../types';
import { formatCurrency } from '../utils/currency';

interface LoanDisplayCardProps {
  loan: Loan;
  onEdit: (loan: Loan) => void;
  onDelete: (loan: Loan) => void;
}

/**
 * LoanDisplayCard component for displaying loan details
 * This component uses the actual dates from the database
 */
export const LoanDisplayCard: React.FC<LoanDisplayCardProps> = ({ loan, onEdit, onDelete }) => {
  // Use the actual date from the database
  const displayDate = loan.next_payment_date;

  return (
    <div className="bg-gray-800 border border-gray-700 rounded-lg shadow-lg p-4 mb-4">
      <div className="flex justify-between items-start mb-2">
        <h3 className="text-xl font-semibold text-white">{loan.name}</h3>
        <div className="flex space-x-2">
          <button
            onClick={() => onEdit(loan)}
            className="text-blue-400 hover:text-blue-300 transition"
          >
            Edit
          </button>
          <button
            onClick={() => onDelete(loan)}
            className="text-red-400 hover:text-red-300 transition"
          >
            Delete
          </button>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 mt-4">
        <div>
          <p className="text-gray-400 text-sm">Principal</p>
          <p className="text-white font-semibold">{formatCurrency(loan.principal)}</p>
        </div>
        <div>
          <p className="text-gray-400 text-sm">Interest Rate</p>
          <p className="text-white font-semibold">{loan.interest_rate}%</p>
        </div>
        <div>
          <p className="text-gray-400 text-sm">Term</p>
          <p className="text-white font-semibold">{loan.term_months} months</p>
        </div>
        <div>
          <p className="text-gray-400 text-sm">Monthly Payment</p>
          <p className="text-white font-semibold">{formatCurrency(loan.monthly_payment)}</p>
        </div>
        <div>
          <p className="text-gray-400 text-sm">Next Payment Date</p>
          <p className="text-white font-semibold">{displayDate}</p>
        </div>
        <div>
          <p className="text-gray-400 text-sm">Remaining Balance</p>
          <p className="text-white font-semibold">{formatCurrency(loan.remaining_balance)}</p>
        </div>
      </div>
    </div>
  );
};
