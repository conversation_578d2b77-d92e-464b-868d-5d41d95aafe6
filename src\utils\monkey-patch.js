/**
 * Monkey Patch Utility
 * 
 * This utility provides functions to safely override (monkey patch) existing
 * functions in objects without completely replacing the object.
 */

/**
 * Add a monkey patch to an object's method
 * @param {Object} target - The target object to patch
 * @param {string} methodName - The name of the method to patch
 * @param {Function} newImplementation - The new implementation
 * @returns {boolean} - True if patching was successful
 */
export function addMonkeyPatch(target, methodName, newImplementation) {
  if (!target) {
    console.error(`[MonkeyPatch] Target object is null or undefined`);
    return false;
  }
  
  if (typeof target[methodName] !== 'function' && typeof newImplementation !== 'function') {
    console.error(`[MonkeyPatch] ${methodName} is not a function on the target object or the new implementation is not a function`);
    return false;
  }
  
  // Store the original method if it exists
  const originalMethod = target[methodName];
  
  // Replace with the new implementation
  target[methodName] = newImplementation;
  
  // Store the original method on the new function for potential restoration
  target[methodName]._original = originalMethod;
  
  console.log(`[MonkeyPatch] Successfully patched ${methodName}`);
  return true;
}

/**
 * Remove a monkey patch and restore the original method
 * @param {Object} target - The target object
 * @param {string} methodName - The name of the method to restore
 * @returns {boolean} - True if restoration was successful
 */
export function removeMonkeyPatch(target, methodName) {
  if (!target || typeof target[methodName] !== 'function') {
    console.error(`[MonkeyPatch] Cannot restore ${methodName}, target or method invalid`);
    return false;
  }
  
  // Check if we have an original method stored
  if (target[methodName]._original) {
    target[methodName] = target[methodName]._original;
    console.log(`[MonkeyPatch] Restored original ${methodName}`);
    return true;
  }
  
  console.error(`[MonkeyPatch] No original method found for ${methodName}`);
  return false;
}
