
<!DOCTYPE html>
<html>
<head>
    <title>Storage Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 8px 16px; margin: 5px; }
        textarea { width: 100%; height: 200px; margin-top: 10px; }
        .container { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Storage Test</h1>
        <div>
            <label>Key: <input type="text" id="keyInput" value="local_auth_user"></label>
        </div>
        <div style="margin-top: 10px;">
            <label>Value: <input type="text" id="valueInput" value='{"id":"test-user","email":"<EMAIL>"}'></label>
        </div>
        <div style="margin-top: 10px;">
            <button onclick="saveData()">Save Data</button>
            <button onclick="loadData()">Load Data</button>
            <button onclick="listKeys()">List All Keys</button>
            <button onclick="dumpStorage()">Dump All Storage</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        <textarea id="logOutput" readonly></textarea>
    </div>

    <script>
        // Log function
        function log(message) {
            const logElement = document.getElementById('logOutput');
            logElement.value += new Date().toISOString() + ': ' + message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Clear log
        function clearLog() {
            document.getElementById('logOutput').value = '';
        }

        // Save data to storage
        function saveData() {
            const key = document.getElementById('keyInput').value;
            const value = document.getElementById('valueInput').value;
            
            if (!key) {
                log('Error: Key cannot be empty');
                return;
            }

            try {
                log(`Saving data with key "${key}"`);
                log(`Value: ${value}`);
                
                // Use pywebview API if available
                if (window.pywebview) {
                    window.pywebview.api.set_item(key, value);
                    log('Data saved using pywebview API');
                } else {
                    localStorage.setItem(key, value);
                    log('Data saved using localStorage (no pywebview detected)');
                }
            } catch (error) {
                log(`Error saving data: ${error.message || error}`);
            }
        }

        // Load data from storage
        function loadData() {
            const key = document.getElementById('keyInput').value;
            
            if (!key) {
                log('Error: Key cannot be empty');
                return;
            }

            try {
                log(`Loading data with key "${key}"`);
                
                // Use pywebview API if available
                if (window.pywebview) {
                    window.pywebview.api.get_item(key).then(data => {
                        log(`Data loaded: ${data || 'null'}`);
                    }).catch(error => {
                        log(`Error loading data: ${error.message || error}`);
                    });
                } else {
                    const data = localStorage.getItem(key);
                    log(`Data loaded: ${data || 'null'}`);
                }
            } catch (error) {
                log(`Error loading data: ${error.message || error}`);
            }
        }

        // List all keys in storage
        function listKeys() {
            try {
                log('Listing all keys in storage:');
                
                // Use pywebview API if available
                if (window.pywebview) {
                    window.pywebview.api.get_all_keys().then(keysJson => {
                        const keys = JSON.parse(keysJson);
                        if (keys.length === 0) {
                            log('No keys found in storage');
                        } else {
                            keys.forEach(key => log(`- ${key}`));
                        }
                    }).catch(error => {
                        log(`Error listing keys: ${error.message || error}`);
                    });
                } else {
                    const keys = Object.keys(localStorage);
                    if (keys.length === 0) {
                        log('No keys found in storage');
                    } else {
                        keys.forEach(key => log(`- ${key}`));
                    }
                }
            } catch (error) {
                log(`Error listing keys: ${error.message || error}`);
            }
        }

        // Dump all storage contents
        function dumpStorage() {
            try {
                log('Dumping all storage contents:');
                
                // Use pywebview API if available
                if (window.pywebview) {
                    window.pywebview.api.dump_storage().then(storageJson => {
                        const storage = JSON.parse(storageJson);
                        const keys = Object.keys(storage);
                        if (keys.length === 0) {
                            log('Storage is empty');
                        } else {
                            keys.forEach(key => {
                                log(`${key}: ${storage[key]}`);
                            });
                        }
                    }).catch(error => {
                        log(`Error dumping storage: ${error.message || error}`);
                    });
                } else {
                    const keys = Object.keys(localStorage);
                    if (keys.length === 0) {
                        log('Storage is empty');
                    } else {
                        keys.forEach(key => {
                            log(`${key}: ${localStorage.getItem(key)}`);
                        });
                    }
                }
            } catch (error) {
                log(`Error dumping storage: ${error.message || error}`);
            }
        }

        // Check if running in pywebview
        window.addEventListener('pywebviewready', () => {
            log('PyWebView detected and ready!');
        });

        // Log initial state
        window.onload = function() {
            log('Storage test initialized');
            log(`PyWebView available: ${window.pywebview !== undefined}`);
        };
    </script>
</body>
</html>
