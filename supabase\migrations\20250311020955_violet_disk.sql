/*
  # Add Net Worth Tracking Tables

  1. New Tables
    - `assets`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `name` (text)
      - `category` (text) - e.g., real estate, vehicles, investments, etc.
      - `value` (numeric)
      - `acquisition_date` (date)
      - `notes` (text)
      - `created_at` (timestamp)

    - `liabilities`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `name` (text)
      - `category` (text) - e.g., mortgage, credit card, personal loan, etc.
      - `amount` (numeric)
      - `interest_rate` (numeric)
      - `notes` (text)
      - `created_at` (timestamp)

  2. Security
    - Enable RLS on both tables
    - Add policies for authenticated users to manage their own data
*/

-- Create assets table
CREATE TABLE IF NOT EXISTS assets (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  category text NOT NULL,
  value numeric NOT NULL CHECK (value >= 0),
  acquisition_date date,
  notes text,
  created_at timestamptz DEFAULT now()
);

-- Create liabilities table
CREATE TABLE IF NOT EXISTS liabilities (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) NOT NULL,
  name text NOT NULL,
  category text NOT NULL,
  amount numeric NOT NULL CHECK (amount >= 0),
  interest_rate numeric CHECK (interest_rate >= 0),
  notes text,
  created_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE assets ENABLE ROW LEVEL SECURITY;
ALTER TABLE liabilities ENABLE ROW LEVEL SECURITY;

-- Create policies for assets
CREATE POLICY "Users can manage their own assets"
  ON assets
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Create policies for liabilities
CREATE POLICY "Users can manage their own liabilities"
  ON liabilities
  FOR ALL
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);